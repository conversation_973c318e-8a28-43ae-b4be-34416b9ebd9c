#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建大型测试文件用于性能测试
"""

import random
import time

def create_large_test_file(filename="large_test_file.txt", lines=10000):
    """创建大型测试文件"""
    print(f"正在创建包含 {lines:,} 行的测试文件...")
    
    # 预定义的内容模板
    templates = [
        "这是第{line_num}行，包含一些普通的文本内容。",
        "日志记录 {line_num}: 用户操作成功完成，耗时 {time}ms",
        "错误信息 {line_num}: 连接超时，请检查网络设置",
        "数据处理 {line_num}: 处理了 {count} 条记录，状态正常",
        "系统监控 {line_num}: CPU使用率 {cpu}%, 内存使用 {memory}MB",
        "搜索测试 {line_num}: 这行包含搜索关键词，用于测试功能",
        "正则表达式测试 {line_num}: 包含数字 {number} 和字母 {letter}",
        "性能测试 {line_num}: 大文件处理性能验证行",
        "中文内容 {line_num}: 这是中文测试内容，包含各种字符",
        "特殊字符 {line_num}: @#$%^&*()_+{}|:<>?[]\\;'\",./"
    ]
    
    start_time = time.time()
    
    with open(filename, 'w', encoding='utf-8') as f:
        for i in range(lines):
            # 随机选择模板
            template = random.choice(templates)
            
            # 填充变量
            try:
                content = template.format(
                    line_num=i+1,
                    time=random.randint(10, 1000),
                    count=random.randint(100, 9999),
                    cpu=random.randint(10, 95),
                    memory=random.randint(100, 2048),
                    number=random.randint(1000, 9999),
                    letter=''.join(random.choice('abcdefghijklmnopqrstuvwxyz') for _ in range(5))
                )
            except (KeyError, IndexError):
                # 如果格式化失败，使用简单内容
                content = f"第{i+1}行：测试内容"
            
            f.write(content + '\n')
            
            # 显示进度
            if (i + 1) % 1000 == 0:
                progress = (i + 1) / lines * 100
                print(f"进度: {progress:.1f}% ({i+1:,}/{lines:,} 行)")
    
    elapsed_time = time.time() - start_time
    file_size = os.path.getsize(filename) / 1024 / 1024  # MB
    
    print(f"✓ 文件创建完成!")
    print(f"  文件名: {filename}")
    print(f"  行数: {lines:,}")
    print(f"  文件大小: {file_size:.2f} MB")
    print(f"  创建耗时: {elapsed_time:.2f} 秒")
    
    return filename

if __name__ == "__main__":
    import os
    
    # 创建不同大小的测试文件
    test_files = [
        ("small_test.txt", 1000),      # 1K行
        ("medium_test.txt", 10000),    # 10K行
        ("large_test.txt", 100000),    # 100K行
    ]
    
    for filename, lines in test_files:
        if not os.path.exists(filename):
            create_large_test_file(filename, lines)
        else:
            print(f"文件 {filename} 已存在，跳过创建")
    
    print("\n所有测试文件创建完成！")
