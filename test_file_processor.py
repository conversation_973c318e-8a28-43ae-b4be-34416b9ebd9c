#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件处理器功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.file_processor import FileProcessor
from utils.file_utils import get_file_info, validate_file_path
from utils.encoding_utils import detect_encoding


def test_file_processor():
    """测试文件处理器功能"""
    print("=== 测试文件处理器 ===")
    
    # 创建文件处理器实例
    processor = FileProcessor()
    
    # 测试文件路径
    test_file = "test_sample.txt"
    
    if not os.path.exists(test_file):
        print(f"错误：测试文件 {test_file} 不存在")
        return
    
    print(f"\n1. 测试文件信息获取")
    try:
        file_info = get_file_info(test_file)
        print(f"文件名: {file_info['name']}")
        print(f"文件大小: {file_info['size_formatted']}")
        print(f"是否为文本文件: {file_info['is_text_file']}")
        print(f"是否为大文件: {file_info['is_large_file']}")
    except Exception as e:
        print(f"获取文件信息失败: {e}")
    
    print(f"\n2. 测试文件路径验证")
    try:
        validation = validate_file_path(test_file)
        print(f"文件有效性: {validation['is_valid']}")
        print(f"文件存在: {validation['exists']}")
        print(f"可读性: {validation['is_readable']}")
        if validation['errors']:
            print(f"错误信息: {validation['errors']}")
    except Exception as e:
        print(f"文件验证失败: {e}")
    
    print(f"\n3. 测试编码检测")
    try:
        encoding = detect_encoding(test_file)
        print(f"检测到的编码: {encoding}")
    except Exception as e:
        print(f"编码检测失败: {e}")
    
    print(f"\n4. 测试文件打开")
    try:
        file_info = processor.open_file(test_file)
        print(f"文件路径: {file_info['file_path']}")
        print(f"文件大小: {file_info['file_size']} 字节")
        print(f"编码格式: {file_info['encoding']}")
        print(f"编码有效性: {file_info['encoding_valid']}")
    except Exception as e:
        print(f"打开文件失败: {e}")
    
    print(f"\n5. 测试流式读取")
    try:
        line_count = 0
        for line_num, line_content in processor.read_file_stream(test_file):
            line_count += 1
            print(f"行 {line_num}: {line_content[:50]}{'...' if len(line_content) > 50 else ''}")
            
            # 只显示前5行
            if line_count >= 5:
                print("...")
                break
                
        print(f"总共读取了 {line_count} 行")
    except Exception as e:
        print(f"流式读取失败: {e}")
    
    print(f"\n6. 测试文件预览")
    try:
        preview = processor.get_file_preview(test_file, max_lines=3)
        print("文件预览（前3行）:")
        for line_num, line_content in preview:
            print(f"  行 {line_num}: {line_content}")
    except Exception as e:
        print(f"文件预览失败: {e}")
    
    print(f"\n7. 测试文件统计")
    try:
        stats = processor.get_file_stats(test_file)
        print(f"文件路径: {stats['file_path']}")
        print(f"文件大小: {stats['file_size']} 字节")
        print(f"总行数: {stats['line_count']}")
        print(f"编码格式: {stats['encoding']}")
        print("样本内容:")
        for i, sample in enumerate(stats['sample_content'][:3]):
            print(f"  样本 {i+1}: {sample}")
    except Exception as e:
        print(f"文件统计失败: {e}")
    
    print(f"\n8. 测试读取方法选择")
    try:
        method = processor.choose_reading_method(test_file)
        print(f"推荐的读取方法: {method}")
    except Exception as e:
        print(f"选择读取方法失败: {e}")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    test_file_processor()
