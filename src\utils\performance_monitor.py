#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控模块

提供内存使用、CPU使用率等性能监控功能。
"""

import time
import threading
import os
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass

# 尝试导入psutil，如果失败则使用简化版本
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
    print("警告: 未安装psutil库，将使用简化的性能监控")


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    memory_usage_mb: float
    memory_percent: float
    cpu_percent: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    lines_processed: int = 0
    processing_speed: float = 0.0  # 行/秒


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, update_interval: float = 1.0):
        """
        初始化性能监控器
        
        参数:
            update_interval: 更新间隔（秒）
        """
        self.update_interval = update_interval
        self.is_monitoring = False
        self.monitor_thread = None
        self.metrics_history = []
        self.max_history_size = 100
        
        # 回调函数
        self.update_callback: Optional[Callable] = None
        
        # 获取当前进程
        if HAS_PSUTIL:
            self.process = psutil.Process(os.getpid())
            # 初始磁盘IO计数器
            try:
                self.last_disk_io = self.process.io_counters()
            except:
                self.last_disk_io = None
        else:
            self.process = None
            self.last_disk_io = None

        self.last_update_time = time.time()
        
        # 处理统计
        self.start_time = None
        self.lines_processed = 0
    
    def set_update_callback(self, callback: Callable[[PerformanceMetrics], None]):
        """设置更新回调函数"""
        self.update_callback = callback
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.start_time = time.time()
        self.lines_processed = 0
        self.metrics_history.clear()
        
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)
    
    def update_lines_processed(self, lines: int):
        """更新已处理行数"""
        self.lines_processed = lines
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                metrics = self._collect_metrics()
                
                # 添加到历史记录
                self.metrics_history.append(metrics)
                if len(self.metrics_history) > self.max_history_size:
                    self.metrics_history.pop(0)
                
                # 调用回调函数
                if self.update_callback:
                    self.update_callback(metrics)
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                print(f"性能监控错误: {e}")
                time.sleep(self.update_interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        current_time = time.time()

        if HAS_PSUTIL and self.process:
            try:
                # 内存使用
                memory_info = self.process.memory_info()
                memory_usage_mb = memory_info.rss / 1024 / 1024
                memory_percent = self.process.memory_percent()

                # CPU使用率
                cpu_percent = self.process.cpu_percent()

                # 磁盘IO
                disk_read_mb = 0
                disk_write_mb = 0

                if self.last_disk_io:
                    try:
                        current_disk_io = self.process.io_counters()
                        time_delta = current_time - self.last_update_time

                        if time_delta > 0:
                            disk_read_mb = (current_disk_io.read_bytes - self.last_disk_io.read_bytes) / 1024 / 1024 / time_delta
                            disk_write_mb = (current_disk_io.write_bytes - self.last_disk_io.write_bytes) / 1024 / 1024 / time_delta

                        # 更新上次记录
                        self.last_disk_io = current_disk_io
                    except:
                        pass

            except Exception as e:
                print(f"性能监控错误: {e}")
                # 使用默认值
                memory_usage_mb = 0
                memory_percent = 0
                cpu_percent = 0
                disk_read_mb = 0
                disk_write_mb = 0
        else:
            # 简化版本，只提供基本信息
            memory_usage_mb = 0
            memory_percent = 0
            cpu_percent = 0
            disk_read_mb = 0
            disk_write_mb = 0

        # 处理速度
        if self.start_time and current_time > self.start_time:
            processing_speed = self.lines_processed / (current_time - self.start_time)
        else:
            processing_speed = 0

        self.last_update_time = current_time

        return PerformanceMetrics(
            timestamp=current_time,
            memory_usage_mb=memory_usage_mb,
            memory_percent=memory_percent,
            cpu_percent=cpu_percent,
            disk_io_read_mb=disk_read_mb,
            disk_io_write_mb=disk_write_mb,
            lines_processed=self.lines_processed,
            processing_speed=processing_speed
        )
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前性能指标"""
        if self.metrics_history:
            return self.metrics_history[-1]
        return None
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取性能指标摘要"""
        if not self.metrics_history:
            return {}
        
        memory_values = [m.memory_usage_mb for m in self.metrics_history]
        cpu_values = [m.cpu_percent for m in self.metrics_history]
        speed_values = [m.processing_speed for m in self.metrics_history if m.processing_speed > 0]
        
        return {
            'memory_usage': {
                'current': memory_values[-1] if memory_values else 0,
                'max': max(memory_values) if memory_values else 0,
                'avg': sum(memory_values) / len(memory_values) if memory_values else 0
            },
            'cpu_usage': {
                'current': cpu_values[-1] if cpu_values else 0,
                'max': max(cpu_values) if cpu_values else 0,
                'avg': sum(cpu_values) / len(cpu_values) if cpu_values else 0
            },
            'processing_speed': {
                'current': speed_values[-1] if speed_values else 0,
                'max': max(speed_values) if speed_values else 0,
                'avg': sum(speed_values) / len(speed_values) if speed_values else 0
            },
            'total_lines': self.lines_processed,
            'elapsed_time': time.time() - self.start_time if self.start_time else 0
        }
    
    def format_metrics_text(self, metrics: PerformanceMetrics) -> str:
        """格式化性能指标为文本"""
        return (f"内存: {metrics.memory_usage_mb:.1f}MB ({metrics.memory_percent:.1f}%) | "
                f"CPU: {metrics.cpu_percent:.1f}% | "
                f"速度: {metrics.processing_speed:.0f} 行/秒 | "
                f"已处理: {metrics.lines_processed} 行")
    
    def is_memory_usage_high(self, threshold_mb: float = 500) -> bool:
        """检查内存使用是否过高"""
        current = self.get_current_metrics()
        return current and current.memory_usage_mb > threshold_mb
    
    def is_processing_slow(self, threshold_speed: float = 100) -> bool:
        """检查处理速度是否过慢"""
        current = self.get_current_metrics()
        return current and current.processing_speed > 0 and current.processing_speed < threshold_speed
