#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本

使用PyInstaller将大文件过滤器打包为可执行文件。
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def check_requirements():
    """检查打包要求"""
    print("检查打包要求...")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✓ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        return False
    
    # 检查主程序文件
    if not os.path.exists("main.py"):
        print("❌ 主程序文件 main.py 不存在")
        return False
    
    print("✓ 主程序文件存在")
    
    # 检查源代码目录
    if not os.path.exists("src"):
        print("❌ 源代码目录 src 不存在")
        return False
    
    print("✓ 源代码目录存在")
    
    return True


def create_spec_file():
    """创建PyInstaller规格文件"""
    print("创建PyInstaller规格文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('README.md', '.'),
        ('PRODUCT_DESIGN.md', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'queue',
        'threading',
        're',
        'time',
        'os',
        'sys',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='大文件过滤器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩以提高性能
    console=False,
    disable_windowed_traceback=False,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,  # 禁用UPX压缩
    upx_exclude=[],
    name='大文件过滤器',
)
'''
    
    with open('file_filter.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 规格文件创建完成: file_filter.spec")


def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    try:
        # 运行PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'file_filter.spec'
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 可执行文件构建成功")
            return True
        else:
            print("❌ 构建失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False


def create_release_package():
    """创建发布包"""
    print("创建发布包...")
    
    # 创建发布目录
    release_dir = Path("release")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir()
    
    # 复制可执行文件目录
    dist_dir = Path("dist/大文件过滤器")
    if dist_dir.exists():
        # 复制整个目录
        shutil.copytree(dist_dir, release_dir / "大文件过滤器")
        print("✓ 可执行文件目录已复制")
    else:
        print("❌ 可执行文件目录不存在")
        return False
    
    # 复制文档
    docs_to_copy = [
        "README.md",
        "PRODUCT_DESIGN.md",
        "requirements.txt"
    ]
    
    for doc in docs_to_copy:
        if os.path.exists(doc):
            shutil.copy2(doc, release_dir / doc)
            print(f"✓ 已复制: {doc}")
    
    # 创建用户指南
    create_user_guide(release_dir)
    
    # 创建示例文件
    create_sample_files(release_dir)
    
    print(f"✓ 发布包创建完成: {release_dir}")
    return True


def create_user_guide(release_dir):
    """创建用户指南"""
    user_guide = """# 大文件过滤器 - 用户指南

## 快速开始

1. **启动程序**
   - 双击 `大文件过滤器.exe` 启动程序

2. **选择文件**
   - 点击"选择文件"按钮，或直接拖拽文件到程序窗口
   - 支持的文件类型：.txt、.log、.csv、.json、.xml等文本文件

3. **设置搜索条件**
   - 在搜索框中输入要查找的内容
   - 可选择搜索选项：
     - ☑️ 正则表达式：使用正则表达式搜索
     - ☑️ 大小写敏感：区分大小写
     - ☑️ 整词匹配：只匹配完整单词
   - 设置上下文行数：显示匹配行前后的行数

4. **开始搜索**
   - 点击"开始搜索"按钮或按F3键
   - 程序会实时显示搜索进度和结果
   - 可以随时点击"停止"按钮中断搜索

5. **查看结果**
   - 搜索结果显示在下方表格中
   - 双击任意结果行可查看详细信息
   - 状态栏显示匹配数量和搜索统计

6. **保存结果**
   - 点击"保存结果"按钮
   - 选择保存格式：文本文件、CSV文件、HTML文件
   - 可选择是否包含上下文行和行号

## 快捷键

- **Ctrl+O**: 打开文件
- **Ctrl+F**: 聚焦到搜索框
- **F3**: 开始搜索
- **Escape**: 停止搜索
- **Ctrl+S**: 保存结果

## 高级功能

### 正则表达式搜索
勾选"正则表达式"选项后，可以使用正则表达式进行复杂搜索：
- `\\d+`: 匹配数字
- `[a-zA-Z]+`: 匹配字母
- `\\w+@\\w+\\.\\w+`: 匹配邮箱地址
- `\\b\\w{4}\\b`: 匹配4个字母的单词

### 性能优化
- 程序会根据文件大小自动优化搜索参数
- 可通过"工具"菜单查看性能监控信息
- 建议大文件搜索时适当限制结果数量

### 高级导出
- 通过"文件"菜单的"高级导出"功能
- 支持多种格式和自定义选项
- HTML格式支持高亮显示和样式

## 故障排除

### 程序无法启动
- 确保系统支持Windows 7及以上版本
- 检查是否有杀毒软件误报

### 搜索速度慢
- 尝试减少上下文行数
- 限制最大结果数量
- 避免过于复杂的正则表达式

### 内存使用过高
- 程序会自动监控内存使用
- 大文件搜索时会自动优化参数
- 可通过性能监控查看详细信息

## 技术支持

如有问题或建议，请查看项目文档或联系开发者。

---
大文件过滤器 v1.0.0
"""
    
    with open(release_dir / "用户指南.md", 'w', encoding='utf-8') as f:
        f.write(user_guide)
    
    print("✓ 用户指南已创建")


def create_sample_files(release_dir):
    """创建示例文件"""
    sample_dir = release_dir / "示例文件"
    sample_dir.mkdir()
    
    # 创建示例日志文件
    sample_log = """2024-01-01 10:00:01 [INFO] 系统启动成功
2024-01-01 10:00:02 [INFO] 加载配置文件
2024-01-01 10:00:03 [ERROR] 连接数据库失败: 超时
2024-01-01 10:00:04 [INFO] 重试连接数据库
2024-01-01 10:00:05 [INFO] 数据库连接成功
2024-01-01 10:00:06 [INFO] 用户登录: admin
2024-01-01 10:00:07 [WARNING] 磁盘空间不足
2024-01-01 10:00:08 [INFO] 处理请求: GET /api/users
2024-01-01 10:00:09 [ERROR] 处理失败: 权限不足
2024-01-01 10:00:10 [INFO] 用户注销: admin"""
    
    with open(sample_dir / "示例日志.log", 'w', encoding='utf-8') as f:
        f.write(sample_log)
    
    # 创建示例数据文件
    sample_data = """姓名,年龄,城市,邮箱
张三,25,北京,<EMAIL>
李四,30,上海,<EMAIL>
王五,28,广州,<EMAIL>
赵六,35,深圳,<EMAIL>
钱七,22,杭州,<EMAIL>"""
    
    with open(sample_dir / "示例数据.csv", 'w', encoding='utf-8') as f:
        f.write(sample_data)
    
    print("✓ 示例文件已创建")


def cleanup():
    """清理临时文件"""
    print("清理临时文件...")
    
    temp_dirs = ["build", "__pycache__"]
    temp_files = ["file_filter.spec"]
    
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"✓ 已删除: {temp_dir}")
    
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            os.remove(temp_file)
            print(f"✓ 已删除: {temp_file}")


def main():
    """主函数"""
    print("大文件过滤器 - 打包脚本")
    print("=" * 50)
    
    # 检查要求
    if not check_requirements():
        return False
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if not build_executable():
        return False
    
    # 创建发布包
    if not create_release_package():
        return False
    
    # 清理临时文件
    cleanup()
    
    print("\n" + "=" * 50)
    print("🎉 打包完成！")
    print("发布文件位于 release/ 目录")
    print("=" * 50)
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
