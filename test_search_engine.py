#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试搜索引擎功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.search_engine import SearchEngine, SearchConfig
from core.filter_manager import FilterManager


def test_search_engine():
    """测试搜索引擎功能"""
    print("=== 测试搜索引擎 ===")
    
    # 创建搜索引擎实例
    engine = SearchEngine()
    
    # 测试文本
    test_text = "这是一个测试文件，用于验证大文件过滤器的功能。"
    
    print(f"\n1. 测试基本文本搜索")
    print(f"测试文本: {test_text}")
    
    # 测试普通搜索
    result = engine.search_text(test_text, "测试", regex=False, case_sensitive=False)
    print(f"搜索'测试': {result}")
    
    result = engine.search_text(test_text, "不存在", regex=False, case_sensitive=False)
    print(f"搜索'不存在': {result}")
    
    print(f"\n2. 测试正则表达式搜索")
    result = engine.search_text(test_text, r"测试.*文件", regex=True, case_sensitive=False)
    print(f"正则搜索'测试.*文件': {result}")
    
    result = engine.search_text(test_text, r"\d+", regex=True, case_sensitive=False)
    print(f"正则搜索数字'\\d+': {result}")
    
    print(f"\n3. 测试搜索位置")
    positions = engine.search_with_positions(test_text, "测试", regex=False, case_sensitive=False)
    print(f"'测试'的位置: {positions}")
    
    print(f"\n4. 测试模式验证")
    validation = engine.validate_pattern("测试", regex=False)
    print(f"普通模式'测试'验证: {validation}")
    
    validation = engine.validate_pattern(r"[a-z+", regex=True)
    print(f"错误正则'[a-z+'验证: {validation}")
    
    print(f"\n5. 测试高亮显示")
    highlighted = engine.highlight_matches(test_text, positions, '<mark>', '</mark>')
    print(f"高亮结果: {highlighted}")
    
    print(f"\n6. 测试多模式搜索")
    patterns = ["测试", "文件", "不存在"]
    result_or = engine.search_multiple_patterns(test_text, patterns, operator='OR')
    result_and = engine.search_multiple_patterns(test_text, patterns, operator='AND')
    print(f"多模式OR搜索: {result_or}")
    print(f"多模式AND搜索: {result_and}")


def test_filter_manager():
    """测试过滤管理器功能"""
    print(f"\n=== 测试过滤管理器 ===")
    
    # 创建过滤管理器实例
    manager = FilterManager()
    
    # 测试文件路径
    test_file = "test_sample.txt"
    
    if not os.path.exists(test_file):
        print(f"错误：测试文件 {test_file} 不存在")
        return
    
    print(f"\n1. 测试请求验证")
    config = SearchConfig(pattern="测试", regex=False, case_sensitive=False, context_lines=1)
    validation = manager.validate_filter_request(test_file, config)
    print(f"验证结果: {validation['is_valid']}")
    if validation['errors']:
        print(f"错误: {validation['errors']}")
    if validation['warnings']:
        print(f"警告: {validation['warnings']}")
    
    print(f"\n2. 测试同步过滤")
    try:
        result = manager.filter_file(test_file, config)
        if result['success']:
            print(f"搜索成功!")
            print(f"找到 {len(result['results'])} 个匹配项")
            print(f"搜索耗时: {result['elapsed_time']:.3f} 秒")
            
            # 显示前3个结果
            for i, search_result in enumerate(result['results'][:3]):
                print(f"  结果 {i+1}: 行 {search_result.line_number} - {search_result.line_content[:50]}...")
                
            # 显示摘要
            summary = result['summary']
            print(f"摘要: 总匹配数={summary['total_matches']}, 唯一行数={summary['unique_lines']}")
        else:
            print(f"搜索失败: {result['errors']}")
    except Exception as e:
        print(f"搜索异常: {e}")
    
    print(f"\n3. 测试异步过滤")
    
    # 设置回调函数
    def progress_callback(progress):
        print(f"进度: {progress.progress_percent:.1f}% - {progress.status} - 找到 {progress.matches_found} 个匹配")
    
    def result_callback(result):
        print(f"新结果: 行 {result.line_number}")
    
    def completion_callback(result):
        if result['success']:
            print(f"异步搜索完成! 找到 {len(result['results'])} 个匹配项")
        else:
            print(f"异步搜索失败: {result['errors']}")
    
    manager.set_callbacks(progress_callback, result_callback, completion_callback)
    
    try:
        # 启动异步搜索
        config2 = SearchConfig(pattern="行", regex=False, case_sensitive=False, context_lines=0)
        manager.filter_file_async(test_file, config2)
        
        # 等待完成
        import time
        while manager.is_filter_running():
            time.sleep(0.1)
            
    except Exception as e:
        print(f"异步搜索异常: {e}")
    
    print(f"\n4. 测试结果保存")
    if manager.get_current_results():
        output_file = "search_results.txt"
        success = manager.save_results_to_file(output_file, include_context=True, include_line_numbers=True)
        if success:
            print(f"结果已保存到: {output_file}")
        else:
            print("保存结果失败")


if __name__ == "__main__":
    test_search_engine()
    test_filter_manager()
