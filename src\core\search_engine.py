#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索引擎模块

负责文本搜索、正则表达式匹配等搜索相关功能。
"""

import re
import time
import threading
try:
    import queue
except ImportError:
    import Queue as queue  # Python 2 兼容
from typing import List, Dict, Any, Generator, Optional, Tuple
from dataclasses import dataclass


@dataclass
class SearchResult:
    """搜索结果数据类"""
    line_number: int
    line_content: str
    match_positions: List[Tuple[int, int]]  # 匹配位置 (start, end)
    context_before: List[str] = None
    context_after: List[str] = None


@dataclass
class SearchConfig:
    """搜索配置数据类"""
    pattern: str
    regex: bool = False
    case_sensitive: bool = False
    whole_word: bool = False
    context_lines: int = 0
    max_results: int = 1000
    thread_count: int = 1


class SearchEngine:
    """搜索引擎类 - 负责各种搜索功能"""

    def __init__(self):
        """初始化搜索引擎"""
        self.compiled_patterns = {}  # 缓存编译的正则表达式
        self.search_stats = {
            'total_lines_searched': 0,
            'matches_found': 0,
            'search_time': 0,
            'last_search_config': None
        }

    def compile_pattern(self, pattern: str, regex: bool = False,
                       case_sensitive: bool = False, whole_word: bool = False) -> re.Pattern:
        """
        编译搜索模式为正则表达式

        参数:
            pattern: 搜索模式
            regex: 是否为正则表达式
            case_sensitive: 是否大小写敏感
            whole_word: 是否整词匹配

        返回:
            编译后的正则表达式对象
        """
        # 创建缓存键
        cache_key = (pattern, regex, case_sensitive, whole_word)

        # 检查缓存
        if cache_key in self.compiled_patterns:
            return self.compiled_patterns[cache_key]

        try:
            if regex:
                # 直接使用正则表达式
                regex_pattern = pattern
            else:
                # 转义特殊字符
                regex_pattern = re.escape(pattern)

            # 添加整词匹配
            if whole_word:
                regex_pattern = r'\b' + regex_pattern + r'\b'

            # 设置标志
            flags = 0
            if not case_sensitive:
                flags |= re.IGNORECASE

            # 编译正则表达式
            compiled = re.compile(regex_pattern, flags)

            # 缓存结果
            self.compiled_patterns[cache_key] = compiled

            return compiled

        except re.error as e:
            raise ValueError(f"正则表达式编译失败: {str(e)}")

    def search_in_text(self, text: str, pattern: re.Pattern) -> List[Tuple[int, int]]:
        """
        在文本中搜索模式并返回匹配位置

        参数:
            text: 要搜索的文本
            pattern: 编译后的正则表达式

        返回:
            匹配位置列表 [(start, end), ...]
        """
        matches = []
        for match in pattern.finditer(text):
            matches.append((match.start(), match.end()))
        return matches

    def search_text(self, text: str, pattern: str, regex: bool = False,
                   case_sensitive: bool = False, whole_word: bool = False) -> bool:
        """
        在文本中搜索指定模式

        参数:
            text: 要搜索的文本
            pattern: 搜索模式
            regex: 是否使用正则表达式
            case_sensitive: 是否大小写敏感
            whole_word: 是否整词匹配

        返回:
            是否匹配
        """
        try:
            compiled_pattern = self.compile_pattern(pattern, regex, case_sensitive, whole_word)
            return bool(compiled_pattern.search(text))
        except Exception:
            return False

    def search_with_positions(self, text: str, pattern: str, regex: bool = False,
                            case_sensitive: bool = False, whole_word: bool = False) -> List[Tuple[int, int]]:
        """
        在文本中搜索并返回匹配位置

        参数:
            text: 要搜索的文本
            pattern: 搜索模式
            regex: 是否使用正则表达式
            case_sensitive: 是否大小写敏感
            whole_word: 是否整词匹配

        返回:
            匹配位置列表
        """
        try:
            compiled_pattern = self.compile_pattern(pattern, regex, case_sensitive, whole_word)
            return self.search_in_text(text, compiled_pattern)
        except Exception:
            return []

    def search_file_stream(self, file_stream: Generator[Tuple[int, str], None, None],
                          config: SearchConfig) -> Generator[SearchResult, None, None]:
        """
        在文件流中搜索

        参数:
            file_stream: 文件流生成器 (line_number, line_content)
            config: 搜索配置

        返回:
            搜索结果生成器
        """
        start_time = time.time()
        matches_found = 0
        lines_searched = 0

        # 编译搜索模式
        try:
            compiled_pattern = self.compile_pattern(
                config.pattern, config.regex,
                config.case_sensitive, config.whole_word
            )
        except ValueError as e:
            raise e

        # 用于存储上下文的缓冲区
        context_buffer = []
        pending_results = []

        try:
            for line_number, line_content in file_stream:
                lines_searched += 1

                # 更新上下文缓冲区
                if config.context_lines > 0:
                    context_buffer.append((line_number, line_content))
                    if len(context_buffer) > config.context_lines * 2 + 1:
                        context_buffer.pop(0)

                # 搜索当前行
                match_positions = self.search_in_text(line_content, compiled_pattern)

                if match_positions:
                    matches_found += 1

                    # 创建搜索结果
                    result = SearchResult(
                        line_number=line_number,
                        line_content=line_content,
                        match_positions=match_positions
                    )

                    # 添加上下文
                    if config.context_lines > 0:
                        result.context_before = []
                        result.context_after = []

                        # 获取前面的上下文
                        buffer_len = len(context_buffer)
                        if buffer_len > 1:
                            start_idx = max(0, buffer_len - config.context_lines - 1)
                            end_idx = buffer_len - 1
                            for i in range(start_idx, end_idx):
                                result.context_before.append(context_buffer[i][1])

                    # 如果需要后续上下文，先存储结果
                    if config.context_lines > 0:
                        pending_results.append((result, 0))  # (result, lines_collected)
                    else:
                        yield result

                # 处理待处理的结果（收集后续上下文）
                if config.context_lines > 0:
                    completed_results = []
                    for i, (pending_result, lines_collected) in enumerate(pending_results):
                        if lines_collected < config.context_lines:
                            # 如果当前行不是匹配行本身，添加到后续上下文
                            if line_number != pending_result.line_number:
                                pending_result.context_after.append(line_content)
                                pending_results[i] = (pending_result, lines_collected + 1)
                        else:
                            # 上下文收集完成
                            completed_results.append(i)

                    # 输出完成的结果
                    for idx in reversed(completed_results):
                        result, _ = pending_results.pop(idx)
                        yield result

                # 检查是否达到最大结果数
                if matches_found >= config.max_results:
                    break

            # 输出剩余的待处理结果
            if config.context_lines > 0:
                for pending_result, _ in pending_results:
                    yield pending_result

        finally:
            # 更新统计信息
            search_time = time.time() - start_time
            self.search_stats.update({
                'total_lines_searched': lines_searched,
                'matches_found': matches_found,
                'search_time': search_time,
                'last_search_config': config
            })

    def get_search_stats(self) -> Dict[str, Any]:
        """
        获取搜索统计信息

        返回:
            统计信息字典
        """
        return self.search_stats.copy()

    def clear_pattern_cache(self):
        """清除正则表达式缓存"""
        self.compiled_patterns.clear()

    def validate_pattern(self, pattern: str, regex: bool = False) -> Dict[str, Any]:
        """
        验证搜索模式的有效性

        参数:
            pattern: 搜索模式
            regex: 是否为正则表达式

        返回:
            验证结果字典
        """
        result = {
            'is_valid': False,
            'error_message': None,
            'pattern_type': 'regex' if regex else 'text'
        }

        if not pattern:
            result['error_message'] = '搜索模式不能为空'
            return result

        try:
            if regex:
                # 验证正则表达式语法
                re.compile(pattern)

            result['is_valid'] = True

        except re.error as e:
            result['error_message'] = f'正则表达式语法错误: {str(e)}'
        except Exception as e:
            result['error_message'] = f'模式验证失败: {str(e)}'

        return result

    def highlight_matches(self, text: str, match_positions: List[Tuple[int, int]],
                         highlight_start: str = '<mark>', highlight_end: str = '</mark>') -> str:
        """
        在文本中高亮显示匹配的部分

        参数:
            text: 原始文本
            match_positions: 匹配位置列表
            highlight_start: 高亮开始标记
            highlight_end: 高亮结束标记

        返回:
            高亮后的文本
        """
        if not match_positions:
            return text

        # 按位置排序
        sorted_positions = sorted(match_positions, key=lambda x: x[0])

        # 从后往前插入标记，避免位置偏移
        result = text
        for start, end in reversed(sorted_positions):
            result = result[:start] + highlight_start + result[start:end] + highlight_end + result[end:]

        return result

    def search_multiple_patterns(self, text: str, patterns: List[str],
                                operator: str = 'OR', **kwargs) -> bool:
        """
        使用多个模式搜索文本

        参数:
            text: 要搜索的文本
            patterns: 搜索模式列表
            operator: 逻辑操作符 ('OR', 'AND')
            **kwargs: 其他搜索参数

        返回:
            是否匹配
        """
        if not patterns:
            return False

        results = []
        for pattern in patterns:
            try:
                match = self.search_text(text, pattern, **kwargs)
                results.append(match)
            except Exception:
                results.append(False)

        if operator.upper() == 'AND':
            return all(results)
        else:  # OR
            return any(results)

    def get_match_context(self, lines: List[str], match_line_index: int,
                         context_lines: int = 2) -> Dict[str, List[str]]:
        """
        获取匹配行的上下文

        参数:
            lines: 所有行的列表
            match_line_index: 匹配行的索引
            context_lines: 上下文行数

        返回:
            包含上下文的字典
        """
        total_lines = len(lines)

        # 计算上下文范围
        start_idx = max(0, match_line_index - context_lines)
        end_idx = min(total_lines, match_line_index + context_lines + 1)

        context = {
            'before': [],
            'match': lines[match_line_index] if 0 <= match_line_index < total_lines else '',
            'after': []
        }

        # 获取前面的上下文
        for i in range(start_idx, match_line_index):
            context['before'].append(lines[i])

        # 获取后面的上下文
        for i in range(match_line_index + 1, end_idx):
            context['after'].append(lines[i])

        return context

    def estimate_search_time(self, file_size: int, pattern_complexity: str = 'simple') -> float:
        """
        估算搜索时间

        参数:
            file_size: 文件大小（字节）
            pattern_complexity: 模式复杂度 ('simple', 'medium', 'complex')

        返回:
            估算的搜索时间（秒）
        """
        # 基础处理速度（字节/秒）
        base_speed = {
            'simple': 10 * 1024 * 1024,    # 10MB/s
            'medium': 5 * 1024 * 1024,     # 5MB/s
            'complex': 2 * 1024 * 1024     # 2MB/s
        }

        speed = base_speed.get(pattern_complexity, base_speed['simple'])
        estimated_time = file_size / speed

        return max(0.1, estimated_time)  # 最少0.1秒

    def create_search_summary(self, results: List[SearchResult]) -> Dict[str, Any]:
        """
        创建搜索结果摘要

        参数:
            results: 搜索结果列表

        返回:
            搜索摘要字典
        """
        if not results:
            return {
                'total_matches': 0,
                'unique_lines': 0,
                'first_match_line': None,
                'last_match_line': None,
                'average_matches_per_line': 0
            }

        total_matches = sum(len(result.match_positions) for result in results)
        unique_lines = len(set(result.line_number for result in results))
        line_numbers = [result.line_number for result in results]

        return {
            'total_matches': total_matches,
            'unique_lines': unique_lines,
            'first_match_line': min(line_numbers),
            'last_match_line': max(line_numbers),
            'average_matches_per_line': total_matches / unique_lines if unique_lines > 0 else 0,
            'line_numbers': sorted(set(line_numbers))
        }

    def search_file_stream_multithreaded(self, file_stream: Generator[Tuple[int, str], None, None],
                                        config: SearchConfig) -> Generator[SearchResult, None, None]:
        """
        多线程搜索文件流

        参数:
            file_stream: 文件流生成器 (line_number, line_content)
            config: 搜索配置

        返回:
            搜索结果生成器
        """
        if config.thread_count <= 1:
            # 单线程模式，使用原有方法
            yield from self.search_file_stream(file_stream, config)
            return

        start_time = time.time()
        matches_found = 0
        lines_searched = 0

        # 编译搜索模式
        try:
            compiled_pattern = self.compile_pattern(
                config.pattern, config.regex,
                config.case_sensitive, config.whole_word
            )
        except ValueError as e:
            raise e

        # 创建工作队列和结果队列
        # 增加队列大小以减少阻塞
        work_queue = queue.Queue(maxsize=config.thread_count * 20)
        result_queue = queue.Queue(maxsize=config.thread_count * 10)

        # 工作线程函数 - 批量处理
        def worker():
            while True:
                try:
                    batch = work_queue.get(timeout=1)
                    if batch is None:  # 结束信号
                        break

                    # 处理整个批次
                    batch_results = []
                    for line_number, line_content in batch:
                        # 搜索当前行
                        match_positions = self.search_in_text(line_content, compiled_pattern)

                        if match_positions:
                            result = SearchResult(
                                line_number=line_number,
                                line_content=line_content,
                                match_positions=match_positions
                            )
                            batch_results.append(result)

                    # 批量放入结果队列
                    if batch_results:
                        result_queue.put(batch_results)

                    work_queue.task_done()

                except queue.Empty:
                    continue
                except Exception as e:
                    print(f"工作线程错误: {e}")
                    break

        # 启动工作线程
        threads = []
        for _ in range(config.thread_count):
            t = threading.Thread(target=worker, daemon=True)
            t.start()
            threads.append(t)

        # 生产者线程：将文件行批量放入工作队列
        def producer():
            try:
                batch = []
                # 动态批次大小：根据线程数调整
                batch_size = max(50, 1000 // config.thread_count)  # 线程越多，批次越小

                for line_number, line_content in file_stream:
                    batch.append((line_number, line_content))

                    # 当批次满了或达到最大结果数时，发送批次
                    if len(batch) >= batch_size or matches_found >= config.max_results:
                        work_queue.put(batch)
                        batch = []

                    if matches_found >= config.max_results:
                        break

                # 发送剩余的批次
                if batch:
                    work_queue.put(batch)

                # 发送结束信号
                for _ in range(config.thread_count):
                    work_queue.put(None)

            except Exception as e:
                print(f"生产者线程错误: {e}")

        producer_thread = threading.Thread(target=producer, daemon=True)
        producer_thread.start()

        # 收集结果 - 批量处理
        results_collected = []

        try:
            while matches_found < config.max_results:
                try:
                    batch_results = result_queue.get(timeout=2)

                    # 处理批量结果
                    for result in batch_results:
                        if matches_found >= config.max_results:
                            break

                        matches_found += 1
                        results_collected.append(result)

                        # 如果需要上下文，暂时存储结果
                        if config.context_lines > 0:
                            # 注意：多线程模式下上下文处理比较复杂
                            # 这里简化处理，不添加上下文
                            pass

                        yield result

                except queue.Empty:
                    # 检查是否所有线程都完成了
                    if not producer_thread.is_alive() and work_queue.empty():
                        # 等待一小段时间看是否还有结果
                        try:
                            batch_results = result_queue.get(timeout=0.5)
                            for result in batch_results:
                                if matches_found >= config.max_results:
                                    break
                                matches_found += 1
                                results_collected.append(result)
                                yield result
                        except queue.Empty:
                            break
                    continue

        finally:
            # 等待所有线程完成
            producer_thread.join(timeout=2)
            for t in threads:
                t.join(timeout=1)

            # 更新统计信息
            search_time = time.time() - start_time
            self.search_stats.update({
                'total_lines_searched': lines_searched,
                'matches_found': matches_found,
                'search_time': search_time,
                'last_search_config': config
            })
