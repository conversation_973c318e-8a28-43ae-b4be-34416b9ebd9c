#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合功能测试

测试大文件过滤器的所有主要功能。
"""

import sys
import os
import time
import tempfile

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.file_processor import FileProcessor
from core.search_engine import SearchEngine, SearchConfig
from core.filter_manager import FilterManager
from utils.file_utils import get_file_info, validate_file_path
from utils.encoding_utils import detect_encoding


def create_test_file(content, filename="test_file.txt"):
    """创建测试文件"""
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    return filename


def test_file_processor():
    """测试文件处理器"""
    print("=== 测试文件处理器 ===")
    
    # 创建测试文件
    test_content = """第一行：包含测试关键词
第二行：普通内容
第三行：另一个测试内容
第四行：包含数字123和456
第五行：正则表达式测试 [a-z]+
第六行：最后一行"""
    
    test_file = create_test_file(test_content)
    
    try:
        processor = FileProcessor()
        
        # 测试文件打开
        file_info = processor.open_file(test_file)
        print(f"✓ 文件打开成功: {file_info['file_path']}")
        print(f"  文件大小: {file_info['file_size']} 字节")
        print(f"  编码格式: {file_info['encoding']}")
        
        # 测试流式读取
        lines_read = 0
        for line_num, line_content in processor.read_file_stream(test_file):
            lines_read += 1
            print(f"  行 {line_num}: {line_content[:30]}...")
        
        print(f"✓ 流式读取完成，共读取 {lines_read} 行")
        
        # 测试文件统计
        stats = processor.get_file_stats(test_file)
        print(f"✓ 文件统计: 总行数 {stats['line_count']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 文件处理器测试失败: {e}")
        return False
    finally:
        if os.path.exists(test_file):
            os.remove(test_file)


def test_search_engine():
    """测试搜索引擎"""
    print("\n=== 测试搜索引擎 ===")
    
    engine = SearchEngine()
    test_text = "这是一个测试文件，包含测试关键词和数字123"
    
    try:
        # 测试基本搜索
        result = engine.search_text(test_text, "测试")
        print(f"✓ 基本搜索 '测试': {result}")
        
        # 测试正则表达式搜索
        result = engine.search_text(test_text, r"\d+", regex=True)
        print(f"✓ 正则搜索 '\\d+': {result}")
        
        # 测试位置搜索
        positions = engine.search_with_positions(test_text, "测试")
        print(f"✓ 位置搜索 '测试': {positions}")
        
        # 测试模式验证
        validation = engine.validate_pattern("测试", regex=False)
        print(f"✓ 模式验证: {validation['is_valid']}")
        
        # 测试错误正则
        validation = engine.validate_pattern("[a-z+", regex=True)
        print(f"✓ 错误正则验证: {validation['is_valid']} - {validation['error_message']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 搜索引擎测试失败: {e}")
        return False


def test_filter_manager():
    """测试过滤管理器"""
    print("\n=== 测试过滤管理器 ===")
    
    # 创建测试文件
    test_content = """第一行：包含测试关键词
第二行：普通内容行
第三行：另一个测试内容
第四行：包含数字123和456
第五行：正则表达式测试内容
第六行：最后一行包含测试"""
    
    test_file = create_test_file(test_content, "filter_test.txt")
    
    try:
        manager = FilterManager()
        
        # 测试同步过滤
        config = SearchConfig(
            pattern="测试",
            regex=False,
            case_sensitive=False,
            whole_word=False,
            context_lines=1,
            max_results=100
        )
        
        result = manager.filter_file(test_file, config)
        
        if result['success']:
            print(f"✓ 同步过滤成功，找到 {len(result['results'])} 个匹配项")
            
            # 显示结果
            for i, search_result in enumerate(result['results'][:3]):
                print(f"  结果 {i+1}: 行 {search_result.line_number} - {search_result.line_content[:30]}...")
            
            # 测试结果保存
            output_file = "test_output.txt"
            success = manager.save_results_to_file(output_file, include_context=True)
            if success:
                print(f"✓ 结果保存成功: {output_file}")
                if os.path.exists(output_file):
                    os.remove(output_file)
            
        else:
            print(f"✗ 同步过滤失败: {result['errors']}")
            return False
        
        # 测试异步过滤
        print("开始异步过滤测试...")
        
        async_completed = False
        async_results = []
        
        def progress_callback(progress):
            print(f"  进度: {progress.progress_percent:.1f}% - {progress.status}")
        
        def result_callback(result):
            async_results.append(result)
        
        def completion_callback(result):
            nonlocal async_completed
            async_completed = True
            if result['success']:
                print(f"✓ 异步过滤完成，找到 {len(result['results'])} 个匹配项")
            else:
                print(f"✗ 异步过滤失败: {result['errors']}")
        
        manager.set_callbacks(progress_callback, result_callback, completion_callback)
        manager.filter_file_async(test_file, config)
        
        # 等待异步完成
        timeout = 10
        start_time = time.time()
        while not async_completed and (time.time() - start_time) < timeout:
            time.sleep(0.1)
        
        if async_completed:
            print("✓ 异步过滤测试完成")
        else:
            print("✗ 异步过滤超时")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 过滤管理器测试失败: {e}")
        return False
    finally:
        if os.path.exists(test_file):
            os.remove(test_file)


def test_performance():
    """测试性能功能"""
    print("\n=== 测试性能功能 ===")
    
    try:
        # 创建较大的测试文件
        large_content = ""
        for i in range(1000):
            large_content += f"第{i+1}行：这是测试内容，包含一些关键词和数字{i}\n"
        
        large_file = create_test_file(large_content, "large_test.txt")
        
        manager = FilterManager()
        
        # 测试资源估算
        config = SearchConfig(pattern="测试", max_results=100)
        estimation = manager.estimate_search_resources(large_file, config)
        
        print(f"✓ 资源估算完成:")
        print(f"  文件大小: {estimation.get('file_size_mb', 0):.2f} MB")
        print(f"  估算行数: {estimation.get('estimated_lines', 0)}")
        print(f"  预计内存: {estimation.get('estimated_memory_mb', 0):.2f} MB")
        print(f"  预计时间: {estimation.get('estimated_time_seconds', 0):.2f} 秒")
        
        # 测试配置优化
        optimized_config = manager.optimize_search_config(large_file, config)
        print(f"✓ 配置优化完成:")
        print(f"  原始最大结果: {config.max_results}")
        print(f"  优化最大结果: {optimized_config.max_results}")
        
        # 测试性能监控
        if hasattr(manager, 'performance_monitor') and manager.performance_monitor:
            print("✓ 性能监控可用")
        else:
            print("! 性能监控不可用（psutil未安装）")
        
        return True
        
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        return False
    finally:
        if os.path.exists(large_file):
            os.remove(large_file)


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    try:
        # 测试空文件
        empty_file = create_test_file("", "empty.txt")
        processor = FileProcessor()
        
        try:
            file_info = processor.open_file(empty_file)
            print(f"✓ 空文件处理: 大小 {file_info['file_size']} 字节")
        finally:
            os.remove(empty_file)
        
        # 测试不存在的文件
        try:
            processor.open_file("nonexistent.txt")
            print("✗ 应该抛出文件不存在错误")
            return False
        except Exception:
            print("✓ 正确处理不存在的文件")
        
        # 测试无效正则表达式
        engine = SearchEngine()
        validation = engine.validate_pattern("[a-z+", regex=True)
        if not validation['is_valid']:
            print("✓ 正确检测无效正则表达式")
        else:
            print("✗ 未能检测无效正则表达式")
            return False
        
        # 测试特殊字符
        special_content = "特殊字符测试：@#$%^&*()_+{}|:<>?[]\\;'\",./"
        special_file = create_test_file(special_content, "special.txt")
        
        try:
            config = SearchConfig(pattern="@#$", regex=False)
            manager = FilterManager()
            result = manager.filter_file(special_file, config)
            
            if result['success'] and len(result['results']) > 0:
                print("✓ 特殊字符搜索成功")
            else:
                print("✗ 特殊字符搜索失败")
                return False
        finally:
            os.remove(special_file)
        
        return True
        
    except Exception as e:
        print(f"✗ 边界情况测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始综合功能测试...\n")
    
    tests = [
        ("文件处理器", test_file_processor),
        ("搜索引擎", test_search_engine),
        ("过滤管理器", test_filter_manager),
        ("性能功能", test_performance),
        ("边界情况", test_edge_cases)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✓ {test_name} 测试通过")
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败，请检查问题")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
