#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编码工具模块

提供文件编码检测和处理相关的工具函数。
"""

import os

# 尝试导入chardet，如果失败则使用备用方案
try:
    import chardet
    HAS_CHARDET = True
except ImportError:
    HAS_CHARDET = False
    # 在打包版本中不显示警告，静默使用简化检测


def detect_encoding(file_path, sample_size=8192):
    """
    检测文件编码格式

    参数:
        file_path: 文件路径
        sample_size: 采样大小（默认8KB）

    返回:
        编码格式字符串
    """
    try:
        # 读取文件的前几KB来检测编码
        with open(file_path, 'rb') as f:
            raw_data = f.read(sample_size)

        if not raw_data:
            return 'utf-8'  # 空文件默认返回utf-8

        if HAS_CHARDET:
            # 使用chardet检测编码
            result = chardet.detect(raw_data)

            if result and result['encoding']:
                encoding = result['encoding'].lower()
                confidence = result['confidence']

                # 如果置信度太低，使用常见编码
                if confidence < 0.7:
                    return try_common_encodings(raw_data)

                # 处理一些特殊情况
                if encoding in ['gb2312', 'gbk']:
                    return 'gbk'  # 统一使用gbk处理中文
                elif encoding.startswith('utf-8'):
                    return 'utf-8'
                elif encoding.startswith('utf-16'):
                    return 'utf-16'
                else:
                    return encoding

        # 如果没有chardet或检测失败，尝试常见编码
        return try_common_encodings(raw_data)

    except Exception as e:
        print(f"编码检测失败: {e}")
        return 'utf-8'  # 默认返回utf-8


def try_common_encodings(raw_data):
    """
    尝试常见编码格式

    参数:
        raw_data: 原始字节数据

    返回:
        编码格式字符串
    """
    common_encodings = [
        'utf-8', 'gbk', 'gb2312', 'utf-16', 'utf-16le', 'utf-16be',
        'ascii', 'latin1', 'cp1252', 'iso-8859-1'
    ]

    for encoding in common_encodings:
        try:
            raw_data.decode(encoding)
            return encoding
        except UnicodeDecodeError:
            continue

    return 'utf-8'  # 最后的备选方案


def safe_decode(data, encoding='utf-8'):
    """
    安全解码数据

    参数:
        data: 要解码的数据（bytes类型）
        encoding: 编码格式

    返回:
        解码后的字符串
    """
    if isinstance(data, str):
        return data  # 如果已经是字符串，直接返回

    try:
        return data.decode(encoding)
    except UnicodeDecodeError:
        # 如果解码失败，尝试其他编码
        fallback_encodings = ['utf-8', 'gbk', 'latin1']

        for fallback_encoding in fallback_encodings:
            if fallback_encoding != encoding:
                try:
                    return data.decode(fallback_encoding)
                except UnicodeDecodeError:
                    continue

        # 如果所有编码都失败，使用错误替换
        return data.decode(encoding, errors='replace')


def validate_encoding(file_path, encoding):
    """
    验证指定编码是否能正确解码文件

    参数:
        file_path: 文件路径
        encoding: 编码格式

    返回:
        布尔值，表示编码是否有效
    """
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            # 尝试读取前1000个字符
            f.read(1000)
        return True
    except (UnicodeDecodeError, UnicodeError):
        return False
    except Exception:
        return False


def get_file_encoding_info(file_path):
    """
    获取文件的详细编码信息

    参数:
        file_path: 文件路径

    返回:
        字典，包含编码信息
    """
    try:
        # 检测编码
        detected_encoding = detect_encoding(file_path)

        # 验证编码
        is_valid = validate_encoding(file_path, detected_encoding)

        # 获取文件大小
        file_size = os.path.getsize(file_path)

        return {
            'encoding': detected_encoding,
            'is_valid': is_valid,
            'file_size': file_size,
            'confidence': 'high' if is_valid else 'low'
        }

    except Exception as e:
        return {
            'encoding': 'utf-8',
            'is_valid': False,
            'file_size': 0,
            'confidence': 'unknown',
            'error': str(e)
        }
