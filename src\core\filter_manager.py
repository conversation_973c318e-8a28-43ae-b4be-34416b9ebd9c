#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
过滤管理器模块

负责协调文件处理和搜索功能，管理过滤流程。
"""

import os
import threading
import time
from typing import List, Dict, Any, Callable, Optional
from dataclasses import dataclass

# 尝试相对导入，如果失败则使用绝对导入
try:
    from .file_processor import FileProcessor
    from .search_engine import SearchEngine, SearchConfig, SearchResult
    from ..utils.file_utils import get_file_info, validate_file_path
    from ..utils.performance_monitor import PerformanceMonitor
except ImportError:
    import sys
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from core.file_processor import FileProcessor
    from core.search_engine import SearchEngine, SearchConfig, SearchResult
    from utils.file_utils import get_file_info, validate_file_path
    try:
        from utils.performance_monitor import PerformanceMonitor
    except ImportError:
        # 如果psutil不可用，使用简化版本
        PerformanceMonitor = None


@dataclass
class FilterProgress:
    """过滤进度信息"""
    current_line: int = 0
    total_lines: int = 0
    matches_found: int = 0
    progress_percent: float = 0.0
    elapsed_time: float = 0.0
    estimated_remaining: float = 0.0
    status: str = "准备中"


class FilterManager:
    """过滤管理器类 - 协调各个模块完成过滤任务"""

    def __init__(self):
        """初始化过滤管理器"""
        self.file_processor = FileProcessor()
        self.search_engine = SearchEngine()
        self.current_task = None
        self.is_running = False
        self.is_cancelled = False
        self.progress = FilterProgress()
        self.results = []

        # 性能监控
        self.performance_monitor = PerformanceMonitor() if PerformanceMonitor else None

        # 回调函数
        self.progress_callback = None
        self.result_callback = None
        self.completion_callback = None
        self.performance_callback = None

    def set_callbacks(self, progress_callback: Optional[Callable] = None,
                     result_callback: Optional[Callable] = None,
                     completion_callback: Optional[Callable] = None,
                     performance_callback: Optional[Callable] = None):
        """
        设置回调函数

        参数:
            progress_callback: 进度更新回调
            result_callback: 结果更新回调
            completion_callback: 完成回调
            performance_callback: 性能监控回调
        """
        self.progress_callback = progress_callback
        self.result_callback = result_callback
        self.completion_callback = completion_callback
        self.performance_callback = performance_callback

        # 设置性能监控回调
        if self.performance_monitor and performance_callback:
            self.performance_monitor.set_update_callback(performance_callback)

    def validate_filter_request(self, file_path: str, search_config: SearchConfig) -> Dict[str, Any]:
        """
        验证过滤请求

        参数:
            file_path: 文件路径
            search_config: 搜索配置

        返回:
            验证结果
        """
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'file_info': None
        }

        # 验证文件
        file_validation = validate_file_path(file_path)
        if not file_validation['is_valid']:
            result['is_valid'] = False
            result['errors'].extend(file_validation['errors'])
            return result

        # 获取文件信息
        try:
            result['file_info'] = get_file_info(file_path)

            # 检查大文件警告
            if result['file_info']['is_large_file']:
                result['warnings'].append('这是一个大文件，搜索可能需要较长时间')

        except Exception as e:
            result['errors'].append(f'获取文件信息失败: {str(e)}')
            result['is_valid'] = False
            return result

        # 验证搜索模式
        pattern_validation = self.search_engine.validate_pattern(
            search_config.pattern, search_config.regex
        )
        if not pattern_validation['is_valid']:
            result['is_valid'] = False
            result['errors'].append(pattern_validation['error_message'])

        return result

    def filter_file(self, file_path: str, search_config: SearchConfig) -> Dict[str, Any]:
        """
        过滤文件内容（同步方式）

        参数:
            file_path: 文件路径
            search_config: 搜索配置

        返回:
            过滤结果
        """
        # 验证请求
        validation = self.validate_filter_request(file_path, search_config)
        if not validation['is_valid']:
            return {
                'success': False,
                'errors': validation['errors'],
                'results': []
            }

        try:
            # 重置状态
            self.results = []
            self.is_cancelled = False
            self.progress = FilterProgress(status="正在搜索")

            start_time = time.time()

            # 打开文件
            file_info = self.file_processor.open_file(file_path)

            # 获取文件流
            file_stream = self.file_processor.read_file_optimized(file_path)

            # 执行搜索
            search_results = list(self.search_engine.search_file_stream(file_stream, search_config))

            # 更新结果
            self.results = search_results

            # 计算统计信息
            search_stats = self.search_engine.get_search_stats()
            summary = self.search_engine.create_search_summary(search_results)

            elapsed_time = time.time() - start_time

            return {
                'success': True,
                'results': search_results,
                'summary': summary,
                'stats': search_stats,
                'file_info': file_info,
                'elapsed_time': elapsed_time,
                'errors': []
            }

        except Exception as e:
            return {
                'success': False,
                'errors': [str(e)],
                'results': []
            }

    def filter_file_async(self, file_path: str, search_config: SearchConfig):
        """
        异步过滤文件内容

        参数:
            file_path: 文件路径
            search_config: 搜索配置
        """
        if self.is_running:
            raise RuntimeError("已有搜索任务在运行")

        # 创建并启动搜索线程
        self.current_task = threading.Thread(
            target=self._filter_file_worker,
            args=(file_path, search_config)
        )
        self.current_task.daemon = True
        self.current_task.start()

    def _filter_file_worker(self, file_path: str, search_config: SearchConfig):
        """
        异步搜索工作线程

        参数:
            file_path: 文件路径
            search_config: 搜索配置
        """
        self.is_running = True
        self.is_cancelled = False
        self.results = []

        try:
            start_time = time.time()

            # 启动性能监控
            if self.performance_monitor:
                self.performance_monitor.start_monitoring()

            # 验证请求
            validation = self.validate_filter_request(file_path, search_config)
            if not validation['is_valid']:
                if self.completion_callback:
                    self.completion_callback({
                        'success': False,
                        'errors': validation['errors'],
                        'results': []
                    })
                return

            # 更新进度
            self.progress.status = "正在打开文件"
            self._notify_progress()

            # 打开文件
            file_info = self.file_processor.open_file(file_path)

            # 估算总行数（用于进度计算）
            if validation['file_info']['size'] > 0:
                # 粗略估算：平均每行50字节
                estimated_lines = validation['file_info']['size'] // 50
                self.progress.total_lines = max(estimated_lines, 1)

            self.progress.status = "正在搜索"
            self._notify_progress()

            # 获取文件流
            file_stream = self.file_processor.read_file_optimized(file_path)

            # 根据线程数选择搜索方法
            if search_config.thread_count > 1:
                search_method = self.search_engine.search_file_stream_multithreaded
                self.progress.status = f"正在多线程搜索 ({search_config.thread_count} 线程)"
            else:
                search_method = self.search_engine.search_file_stream
                self.progress.status = "正在单线程搜索"

            self._notify_progress()

            # 逐行搜索并实时更新进度
            for result in search_method(file_stream, search_config):
                if self.is_cancelled:
                    break

                self.results.append(result)

                # 更新性能监控
                if self.performance_monitor:
                    self.performance_monitor.update_lines_processed(result.line_number)

                # 更新进度
                self.progress.current_line = result.line_number
                self.progress.matches_found = len(self.results)
                self.progress.elapsed_time = time.time() - start_time

                # 计算进度百分比
                if self.progress.total_lines > 0:
                    self.progress.progress_percent = min(
                        (self.progress.current_line / self.progress.total_lines) * 100, 100
                    )

                # 估算剩余时间
                if self.progress.current_line > 0 and self.progress.total_lines > 0:
                    lines_per_second = self.progress.current_line / self.progress.elapsed_time
                    remaining_lines = self.progress.total_lines - self.progress.current_line
                    self.progress.estimated_remaining = remaining_lines / lines_per_second if lines_per_second > 0 else 0

                self._notify_progress()

                # 通知新结果
                if self.result_callback:
                    self.result_callback(result)

                # 检查内存使用情况
                if self.performance_monitor and self.performance_monitor.is_memory_usage_high():
                    print("警告: 内存使用过高，建议减少结果数量或使用更大的内存")

                # 检查是否达到最大结果数
                if len(self.results) >= search_config.max_results:
                    break

            # 搜索完成
            elapsed_time = time.time() - start_time
            search_stats = self.search_engine.get_search_stats()
            summary = self.search_engine.create_search_summary(self.results)

            # 更新最终进度
            self.progress.status = "搜索完成" if not self.is_cancelled else "搜索已取消"
            self.progress.progress_percent = 100
            self._notify_progress()

            # 通知完成
            if self.completion_callback:
                self.completion_callback({
                    'success': True,
                    'results': self.results,
                    'summary': summary,
                    'stats': search_stats,
                    'file_info': file_info,
                    'elapsed_time': elapsed_time,
                    'cancelled': self.is_cancelled,
                    'errors': []
                })

        except Exception as e:
            # 通知错误
            if self.completion_callback:
                self.completion_callback({
                    'success': False,
                    'errors': [str(e)],
                    'results': self.results,
                    'cancelled': self.is_cancelled
                })
        finally:
            # 停止性能监控
            if self.performance_monitor:
                self.performance_monitor.stop_monitoring()

            self.is_running = False

    def _notify_progress(self):
        """通知进度更新"""
        if self.progress_callback:
            self.progress_callback(self.progress)

    def cancel_filter(self):
        """取消当前的过滤任务"""
        self.is_cancelled = True
        self.file_processor.stop_reading()

        if self.current_task and self.current_task.is_alive():
            # 等待线程结束（最多等待2秒）
            self.current_task.join(timeout=2.0)

    def get_current_progress(self) -> FilterProgress:
        """获取当前进度"""
        return self.progress

    def get_current_results(self) -> List[SearchResult]:
        """获取当前结果"""
        return self.results.copy()

    def is_filter_running(self) -> bool:
        """检查是否有过滤任务在运行"""
        return self.is_running

    def save_results_to_file(self, output_path: str, include_context: bool = True,
                           include_line_numbers: bool = True) -> bool:
        """
        保存搜索结果到文件

        参数:
            output_path: 输出文件路径
            include_context: 是否包含上下文
            include_line_numbers: 是否包含行号

        返回:
            是否成功
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(f"# 搜索结果\n")
                f.write(f"# 总共找到 {len(self.results)} 个匹配项\n\n")

                for result in self.results:
                    # 写入匹配行
                    if include_line_numbers:
                        f.write(f"行 {result.line_number}: {result.line_content}\n")
                    else:
                        f.write(f"{result.line_content}\n")

                    # 写入上下文
                    if include_context and (result.context_before or result.context_after):
                        if result.context_before:
                            f.write("  上下文（前）:\n")
                            for ctx_line in result.context_before:
                                f.write(f"    {ctx_line}\n")

                        if result.context_after:
                            f.write("  上下文（后）:\n")
                            for ctx_line in result.context_after:
                                f.write(f"    {ctx_line}\n")

                    f.write("\n")

            return True

        except Exception as e:
            print(f"保存结果失败: {e}")
            return False

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.performance_monitor:
            return {}

        return self.performance_monitor.get_metrics_summary()

    def optimize_search_config(self, file_path: str, search_config: SearchConfig) -> SearchConfig:
        """
        根据文件大小和系统性能优化搜索配置

        参数:
            file_path: 文件路径
            search_config: 原始搜索配置

        返回:
            优化后的搜索配置
        """
        try:
            file_info = get_file_info(file_path)
            file_size = file_info['size']

            # 创建优化后的配置副本
            optimized_config = SearchConfig(
                pattern=search_config.pattern,
                regex=search_config.regex,
                case_sensitive=search_config.case_sensitive,
                whole_word=search_config.whole_word,
                context_lines=search_config.context_lines,
                max_results=search_config.max_results
            )

            # 根据文件大小调整配置
            if file_size > 100 * 1024 * 1024:  # 大于100MB
                # 大文件优化
                if optimized_config.max_results > 500:
                    optimized_config.max_results = 500
                    print("大文件检测：限制最大结果数为500")

                if optimized_config.context_lines > 2:
                    optimized_config.context_lines = 2
                    print("大文件检测：限制上下文行数为2")

            elif file_size > 1024 * 1024 * 1024:  # 大于1GB
                # 超大文件优化
                if optimized_config.max_results > 200:
                    optimized_config.max_results = 200
                    print("超大文件检测：限制最大结果数为200")

                if optimized_config.context_lines > 1:
                    optimized_config.context_lines = 1
                    print("超大文件检测：限制上下文行数为1")

            return optimized_config

        except Exception as e:
            print(f"配置优化失败: {e}")
            return search_config

    def estimate_search_resources(self, file_path: str, search_config: SearchConfig) -> Dict[str, Any]:
        """
        估算搜索所需资源

        参数:
            file_path: 文件路径
            search_config: 搜索配置

        返回:
            资源估算信息
        """
        try:
            file_info = get_file_info(file_path)
            file_size = file_info['size']

            # 估算行数（假设平均每行50字节）
            estimated_lines = max(file_size // 50, 1)

            # 估算内存使用（每个结果约1KB，加上上下文）
            memory_per_result = 1024 * (1 + search_config.context_lines * 2)
            estimated_memory_mb = (search_config.max_results * memory_per_result) / 1024 / 1024

            # 估算搜索时间
            estimated_time = self.search_engine.estimate_search_time(
                file_size,
                'complex' if search_config.regex else 'simple'
            )

            return {
                'file_size_mb': file_size / 1024 / 1024,
                'estimated_lines': estimated_lines,
                'estimated_memory_mb': estimated_memory_mb,
                'estimated_time_seconds': estimated_time,
                'is_large_file': file_size > 100 * 1024 * 1024,
                'recommendations': self._get_performance_recommendations(file_size, search_config)
            }

        except Exception as e:
            return {'error': str(e)}

    def _get_performance_recommendations(self, file_size: int, search_config: SearchConfig) -> List[str]:
        """获取性能建议"""
        recommendations = []

        if file_size > 1024 * 1024 * 1024:  # 1GB+
            recommendations.append("这是一个超大文件，搜索可能需要较长时间")
            recommendations.append("建议减少最大结果数和上下文行数")

        if search_config.regex:
            recommendations.append("正则表达式搜索比普通搜索慢，请确保模式尽可能具体")

        if search_config.context_lines > 3:
            recommendations.append("较多的上下文行会增加内存使用和处理时间")

        if search_config.max_results > 1000:
            recommendations.append("大量结果会占用更多内存，建议适当限制结果数量")

        return recommendations
