#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多线程搜索功能测试
"""

import sys
import os
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.filter_manager import FilterManager
from core.search_engine import SearchConfig


def test_multithreading_performance():
    """测试多线程搜索性能"""
    print("=== 多线程搜索性能测试 ===")
    
    # 使用之前创建的大文件
    test_file = "large_test.txt"
    if not os.path.exists(test_file):
        print(f"❌ 测试文件 {test_file} 不存在")
        print("请先运行 create_large_test_file.py 创建测试文件")
        return False
    
    file_size = os.path.getsize(test_file) / 1024 / 1024
    print(f"测试文件: {test_file} ({file_size:.2f} MB)")
    
    manager = FilterManager()
    
    # 测试不同线程数的性能
    thread_counts = [1, 2, 4]
    test_pattern = "测试"
    max_results = 500
    
    results = []
    
    for thread_count in thread_counts:
        print(f"\n--- 测试 {thread_count} 线程 ---")
        
        config = SearchConfig(
            pattern=test_pattern,
            regex=False,
            case_sensitive=False,
            whole_word=False,
            context_lines=0,  # 多线程模式下暂不支持上下文
            max_results=max_results,
            thread_count=thread_count
        )
        
        # 执行搜索
        start_time = time.time()
        result = manager.filter_file(test_file, config)
        elapsed_time = time.time() - start_time
        
        if result['success']:
            matches = len(result['results'])
            stats = result.get('stats', {})
            lines_searched = stats.get('total_lines_searched', 0)
            
            # 计算性能指标
            lines_per_second = lines_searched / elapsed_time if elapsed_time > 0 else 0
            mb_per_second = file_size / elapsed_time if elapsed_time > 0 else 0
            
            print(f"✓ 搜索完成")
            print(f"  找到匹配: {matches} 个")
            print(f"  搜索行数: {lines_searched:,}")
            print(f"  耗时: {elapsed_time:.3f} 秒")
            print(f"  速度: {lines_per_second:.0f} 行/秒")
            print(f"  吞吐量: {mb_per_second:.2f} MB/秒")
            
            results.append({
                'thread_count': thread_count,
                'matches': matches,
                'elapsed_time': elapsed_time,
                'lines_per_second': lines_per_second,
                'mb_per_second': mb_per_second
            })
        else:
            print(f"❌ 搜索失败: {result.get('errors', [])}")
    
    # 性能对比分析
    if len(results) > 1:
        print(f"\n{'='*50}")
        print("性能对比分析")
        print('='*50)
        
        baseline = results[0]  # 单线程作为基准
        
        print(f"基准 (1线程): {baseline['elapsed_time']:.3f}s, {baseline['lines_per_second']:.0f} 行/秒")
        
        for result in results[1:]:
            speedup = baseline['elapsed_time'] / result['elapsed_time']
            efficiency = speedup / result['thread_count'] * 100
            
            print(f"{result['thread_count']}线程: {result['elapsed_time']:.3f}s, "
                  f"{result['lines_per_second']:.0f} 行/秒, "
                  f"加速比: {speedup:.2f}x, "
                  f"效率: {efficiency:.1f}%")
    
    return True


def test_max_results_setting():
    """测试最大结果数设置"""
    print(f"\n{'='*50}")
    print("最大结果数设置测试")
    print('='*50)
    
    test_file = "medium_test.txt"
    if not os.path.exists(test_file):
        print(f"❌ 测试文件 {test_file} 不存在")
        return False
    
    manager = FilterManager()
    
    # 测试不同的最大结果数
    max_results_list = [10, 50, 100, 500]
    
    for max_results in max_results_list:
        print(f"\n--- 最大结果数: {max_results} ---")
        
        config = SearchConfig(
            pattern="测试",
            regex=False,
            max_results=max_results,
            thread_count=1
        )
        
        start_time = time.time()
        result = manager.filter_file(test_file, config)
        elapsed_time = time.time() - start_time
        
        if result['success']:
            actual_results = len(result['results'])
            print(f"✓ 设置最大结果数: {max_results}")
            print(f"  实际获得结果: {actual_results}")
            print(f"  搜索耗时: {elapsed_time:.3f} 秒")
            print(f"  结果限制生效: {'是' if actual_results <= max_results else '否'}")
        else:
            print(f"❌ 搜索失败: {result.get('errors', [])}")
    
    return True


def test_thread_count_validation():
    """测试线程数验证"""
    print(f"\n{'='*50}")
    print("线程数验证测试")
    print('='*50)
    
    manager = FilterManager()
    test_file = "small_test.txt"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件 {test_file} 不存在")
        return False
    
    # 测试边界值
    test_cases = [
        (0, "无效线程数 (0)"),
        (1, "单线程"),
        (2, "双线程"),
        (4, "四线程"),
        (8, "八线程"),
        (16, "超出推荐范围 (16)")
    ]
    
    for thread_count, description in test_cases:
        print(f"\n--- {description} ---")
        
        try:
            config = SearchConfig(
                pattern="测试",
                thread_count=thread_count,
                max_results=100
            )
            
            if thread_count <= 0:
                print("❌ 应该拒绝无效线程数")
                continue
            
            result = manager.filter_file(test_file, config)
            
            if result['success']:
                print(f"✓ 线程数 {thread_count} 测试通过")
                print(f"  找到结果: {len(result['results'])} 个")
            else:
                print(f"❌ 搜索失败: {result.get('errors', [])}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
    
    return True


def main():
    """主测试函数"""
    print("多线程搜索功能测试")
    print("=" * 60)
    
    tests = [
        ("多线程性能测试", test_multithreading_performance),
        ("最大结果数测试", test_max_results_setting),
        ("线程数验证测试", test_thread_count_validation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n开始 {test_name}...")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*60)
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
