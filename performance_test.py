#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试

测试大文件过滤器在不同文件大小下的性能表现。
"""

import sys
import os
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.filter_manager import FilterManager
from core.search_engine import SearchConfig


def test_file_performance(file_path, test_name):
    """测试单个文件的性能"""
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    file_size = os.path.getsize(file_path) / 1024 / 1024  # MB
    print(f"\n{'='*60}")
    print(f"测试: {test_name}")
    print(f"文件: {file_path}")
    print(f"大小: {file_size:.2f} MB")
    print('='*60)
    
    manager = FilterManager()
    
    # 测试配置
    test_configs = [
        ("普通文本搜索", SearchConfig(pattern="测试", regex=False, max_results=100)),
        ("正则表达式搜索", SearchConfig(pattern=r"\d+", regex=True, max_results=100)),
        ("大小写敏感搜索", SearchConfig(pattern="Test", case_sensitive=True, max_results=100)),
        ("带上下文搜索", SearchConfig(pattern="错误", context_lines=2, max_results=50)),
    ]
    
    results = []
    
    for config_name, config in test_configs:
        print(f"\n--- {config_name} ---")
        
        # 资源估算
        estimation = manager.estimate_search_resources(file_path, config)
        print(f"预计时间: {estimation.get('estimated_time_seconds', 0):.2f} 秒")
        print(f"预计内存: {estimation.get('estimated_memory_mb', 0):.2f} MB")
        
        # 执行搜索
        start_time = time.time()
        result = manager.filter_file(file_path, config)
        elapsed_time = time.time() - start_time
        
        if result['success']:
            matches = len(result['results'])
            stats = result.get('stats', {})
            lines_searched = stats.get('total_lines_searched', 0)
            
            # 计算性能指标
            lines_per_second = lines_searched / elapsed_time if elapsed_time > 0 else 0
            mb_per_second = file_size / elapsed_time if elapsed_time > 0 else 0
            
            print(f"✓ 搜索完成")
            print(f"  找到匹配: {matches} 个")
            print(f"  搜索行数: {lines_searched:,}")
            print(f"  耗时: {elapsed_time:.3f} 秒")
            print(f"  速度: {lines_per_second:.0f} 行/秒")
            print(f"  吞吐量: {mb_per_second:.2f} MB/秒")
            
            results.append({
                'config_name': config_name,
                'matches': matches,
                'lines_searched': lines_searched,
                'elapsed_time': elapsed_time,
                'lines_per_second': lines_per_second,
                'mb_per_second': mb_per_second
            })
        else:
            print(f"❌ 搜索失败: {result.get('errors', [])}")
    
    return {
        'file_path': file_path,
        'file_size_mb': file_size,
        'test_results': results
    }


def test_memory_usage():
    """测试内存使用情况"""
    print(f"\n{'='*60}")
    print("内存使用测试")
    print('='*60)
    
    try:
        import psutil
        process = psutil.Process()
        
        # 获取初始内存使用
        initial_memory = process.memory_info().rss / 1024 / 1024
        print(f"初始内存使用: {initial_memory:.2f} MB")
        
        # 测试大文件搜索
        if os.path.exists("large_test.txt"):
            manager = FilterManager()
            config = SearchConfig(pattern="测试", max_results=1000)
            
            print("开始大文件搜索...")
            start_time = time.time()
            result = manager.filter_file("large_test.txt", config)
            elapsed_time = time.time() - start_time
            
            # 获取峰值内存使用
            peak_memory = process.memory_info().rss / 1024 / 1024
            memory_increase = peak_memory - initial_memory
            
            print(f"搜索完成，耗时: {elapsed_time:.2f} 秒")
            print(f"峰值内存使用: {peak_memory:.2f} MB")
            print(f"内存增长: {memory_increase:.2f} MB")
            
            if result['success']:
                matches = len(result['results'])
                memory_per_match = memory_increase / matches if matches > 0 else 0
                print(f"找到匹配: {matches} 个")
                print(f"平均每个匹配占用内存: {memory_per_match:.3f} MB")
        
    except ImportError:
        print("psutil未安装，跳过内存测试")


def run_performance_tests():
    """运行所有性能测试"""
    print("开始性能测试...")
    
    # 测试文件列表
    test_files = [
        ("small_test.txt", "小文件测试 (1K行)"),
        ("medium_test.txt", "中等文件测试 (10K行)"),
        ("large_test.txt", "大文件测试 (100K行)"),
    ]
    
    all_results = []
    
    for file_path, test_name in test_files:
        result = test_file_performance(file_path, test_name)
        if result:
            all_results.append(result)
    
    # 内存使用测试
    test_memory_usage()
    
    # 生成性能报告
    print(f"\n{'='*60}")
    print("性能测试总结")
    print('='*60)
    
    for result in all_results:
        print(f"\n文件: {result['file_path']} ({result['file_size_mb']:.2f} MB)")
        
        for test_result in result['test_results']:
            print(f"  {test_result['config_name']}:")
            print(f"    匹配数: {test_result['matches']}")
            print(f"    耗时: {test_result['elapsed_time']:.3f}s")
            print(f"    速度: {test_result['lines_per_second']:.0f} 行/秒")
            print(f"    吞吐量: {test_result['mb_per_second']:.2f} MB/秒")
    
    # 性能建议
    print(f"\n{'='*60}")
    print("性能建议")
    print('='*60)
    
    if all_results:
        # 分析最快的配置
        fastest_config = None
        fastest_speed = 0
        
        for result in all_results:
            for test_result in result['test_results']:
                if test_result['lines_per_second'] > fastest_speed:
                    fastest_speed = test_result['lines_per_second']
                    fastest_config = test_result['config_name']
        
        if fastest_config:
            print(f"✓ 最快的搜索配置: {fastest_config}")
            print(f"  最高速度: {fastest_speed:.0f} 行/秒")
        
        # 分析大文件性能
        large_file_result = next((r for r in all_results if 'large' in r['file_path']), None)
        if large_file_result:
            avg_speed = sum(t['lines_per_second'] for t in large_file_result['test_results']) / len(large_file_result['test_results'])
            print(f"✓ 大文件平均处理速度: {avg_speed:.0f} 行/秒")
            
            if avg_speed > 10000:
                print("  性能评级: 优秀 🎉")
            elif avg_speed > 5000:
                print("  性能评级: 良好 👍")
            elif avg_speed > 1000:
                print("  性能评级: 一般 😐")
            else:
                print("  性能评级: 需要优化 ⚠️")
    
    print("\n性能测试完成！")


if __name__ == "__main__":
    run_performance_tests()
