#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件工具模块

提供文件操作相关的工具函数。
"""

import os
import time
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Any


def get_file_size(file_path: str) -> int:
    """
    获取文件大小

    参数:
        file_path: 文件路径

    返回:
        文件大小（字节）
    """
    try:
        return os.path.getsize(file_path)
    except OSError:
        return 0


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小显示

    参数:
        size_bytes: 文件大小（字节）

    返回:
        格式化的大小字符串
    """
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f} {size_names[i]}"


def get_file_info(file_path: str) -> Dict[str, Any]:
    """
    获取文件的详细信息

    参数:
        file_path: 文件路径

    返回:
        文件信息字典
    """
    try:
        stat = os.stat(file_path)
        path_obj = Path(file_path)

        return {
            'name': path_obj.name,
            'path': str(path_obj.absolute()),
            'size': stat.st_size,
            'size_formatted': format_file_size(stat.st_size),
            'created_time': time.ctime(stat.st_ctime),
            'modified_time': time.ctime(stat.st_mtime),
            'accessed_time': time.ctime(stat.st_atime),
            'extension': path_obj.suffix.lower(),
            'is_text_file': is_text_file(file_path),
            'is_large_file': stat.st_size > 100 * 1024 * 1024  # 大于100MB
        }
    except Exception as e:
        return {
            'name': os.path.basename(file_path),
            'path': file_path,
            'size': 0,
            'size_formatted': '0 B',
            'error': str(e)
        }


def is_text_file(file_path: str) -> bool:
    """
    判断是否为文本文件

    参数:
        file_path: 文件路径

    返回:
        布尔值
    """
    text_extensions = {
        '.txt', '.log', '.csv', '.json', '.xml', '.html', '.htm',
        '.py', '.js', '.css', '.java', '.cpp', '.c', '.h',
        '.md', '.rst', '.ini', '.cfg', '.conf', '.yaml', '.yml'
    }

    extension = Path(file_path).suffix.lower()

    if extension in text_extensions:
        return True

    # 如果扩展名不在列表中，尝试读取文件开头判断
    try:
        with open(file_path, 'rb') as f:
            chunk = f.read(1024)

        # 检查是否包含null字节（二进制文件的特征）
        if b'\x00' in chunk:
            return False

        # 尝试解码为文本
        try:
            chunk.decode('utf-8')
            return True
        except UnicodeDecodeError:
            try:
                chunk.decode('gbk')
                return True
            except UnicodeDecodeError:
                return False

    except Exception:
        return False


def validate_file_path(file_path: str) -> Dict[str, Any]:
    """
    验证文件路径的有效性

    参数:
        file_path: 文件路径

    返回:
        验证结果字典
    """
    result = {
        'is_valid': False,
        'exists': False,
        'is_file': False,
        'is_readable': False,
        'is_text': False,
        'size': 0,
        'errors': []
    }

    try:
        # 检查路径是否存在
        if not os.path.exists(file_path):
            result['errors'].append('文件不存在')
            return result
        result['exists'] = True

        # 检查是否为文件
        if not os.path.isfile(file_path):
            result['errors'].append('不是有效的文件')
            return result
        result['is_file'] = True

        # 检查是否可读
        if not os.access(file_path, os.R_OK):
            result['errors'].append('文件不可读')
            return result
        result['is_readable'] = True

        # 获取文件大小
        result['size'] = get_file_size(file_path)

        # 检查是否为文本文件
        result['is_text'] = is_text_file(file_path)
        if not result['is_text']:
            result['errors'].append('可能不是文本文件')

        # 如果没有错误，标记为有效
        if not result['errors']:
            result['is_valid'] = True

    except Exception as e:
        result['errors'].append(f'验证失败: {str(e)}')

    return result


def create_backup_filename(original_path: str) -> str:
    """
    创建备份文件名

    参数:
        original_path: 原始文件路径

    返回:
        备份文件路径
    """
    path_obj = Path(original_path)
    timestamp = time.strftime('%Y%m%d_%H%M%S')

    backup_name = f"{path_obj.stem}_backup_{timestamp}{path_obj.suffix}"
    return str(path_obj.parent / backup_name)


def safe_write_file(file_path: str, content: str, encoding: str = 'utf-8',
                   create_backup: bool = False) -> bool:
    """
    安全写入文件

    参数:
        file_path: 文件路径
        content: 文件内容
        encoding: 编码格式
        create_backup: 是否创建备份

    返回:
        是否成功
    """
    try:
        # 如果需要备份且文件存在
        if create_backup and os.path.exists(file_path):
            backup_path = create_backup_filename(file_path)
            import shutil
            shutil.copy2(file_path, backup_path)

        # 写入文件
        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)

        return True

    except Exception as e:
        print(f"写入文件失败: {e}")
        return False


def get_file_hash(file_path: str, algorithm: str = 'md5') -> Optional[str]:
    """
    计算文件哈希值

    参数:
        file_path: 文件路径
        algorithm: 哈希算法 ('md5', 'sha1', 'sha256')

    返回:
        哈希值字符串
    """
    try:
        hash_obj = hashlib.new(algorithm)

        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(8192), b""):
                hash_obj.update(chunk)

        return hash_obj.hexdigest()

    except Exception as e:
        print(f"计算哈希失败: {e}")
        return None
