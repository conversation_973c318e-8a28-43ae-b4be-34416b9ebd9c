#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试
"""

import re

def test_basic_search():
    """测试基本搜索功能"""
    print("=== 基本搜索测试 ===")
    
    # 测试文本
    text = "这是一个测试文件，用于验证大文件过滤器的功能。"
    
    # 测试普通搜索
    pattern = "测试"
    if pattern in text:
        print(f"✓ 找到'{pattern}'")
    else:
        print(f"✗ 未找到'{pattern}'")
    
    # 测试正则表达式
    regex_pattern = r"测试.*文件"
    if re.search(regex_pattern, text):
        print(f"✓ 正则匹配'{regex_pattern}'")
    else:
        print(f"✗ 正则不匹配'{regex_pattern}'")
    
    # 测试位置查找
    matches = list(re.finditer(pattern, text))
    if matches:
        for match in matches:
            print(f"✓ '{pattern}'位置: {match.start()}-{match.end()}")
    
    print("基本搜索测试完成")

if __name__ == "__main__":
    test_basic_search()
