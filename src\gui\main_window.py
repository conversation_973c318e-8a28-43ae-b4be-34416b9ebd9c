#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块

这个模块定义了应用程序的主窗口界面和基本布局。
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import time

# 添加相对导入支持
try:
    from ..core.file_processor import FileProcessor
    from ..core.filter_manager import FilterManager
    from ..core.search_engine import SearchConfig
    from ..utils.file_utils import get_file_info, validate_file_path
    from ..utils.encoding_utils import get_file_encoding_info
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from core.file_processor import FileProcessor
    from core.filter_manager import FilterManager
    from core.search_engine import SearchConfig
    from utils.file_utils import get_file_info, validate_file_path
    from utils.encoding_utils import get_file_encoding_info


class MainWindow:
    """主窗口类 - 应用程序的主界面"""
    
    def __init__(self, root):
        """
        初始化主窗口

        参数:
            root: tkinter根窗口对象
        """
        self.root = root
        self.selected_file = None
        self.file_processor = FileProcessor()
        self.filter_manager = FilterManager()
        self.current_file_info = None
        self.current_search_results = []
        self.is_searching = False

        # 设置过滤管理器回调
        self.filter_manager.set_callbacks(
            progress_callback=self.on_search_progress,
            result_callback=self.on_search_result,
            completion_callback=self.on_search_complete,
            performance_callback=self.on_performance_update
        )

        # 初始化界面
        self.setup_ui()

        # 设置快捷键
        self.setup_shortcuts()

        # 设置拖拽支持
        self.setup_drag_drop()
        
    def setup_ui(self):
        """设置用户界面布局"""
        # 创建菜单栏
        self.create_menu_bar()

        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重，使界面可以调整大小
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)

        # 1. 文件选择区域
        self.create_file_selection_area(main_frame)

        # 2. 搜索配置区域
        self.create_search_config_area(main_frame)

        # 3. 结果显示区域
        self.create_result_display_area(main_frame)

        # 4. 状态栏
        self.create_status_bar(main_frame)
        
    def create_file_selection_area(self, parent):
        """创建文件选择区域"""
        # 文件选择标签
        file_label = ttk.Label(parent, text="选择文件:")
        file_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        # 文件路径显示框
        self.file_path_var = tk.StringVar()
        self.file_path_var.set("请选择要过滤的文件...")
        
        file_entry = ttk.Entry(parent, textvariable=self.file_path_var, state="readonly")
        file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=(0, 5))
        
        # 选择文件按钮
        select_btn = ttk.Button(parent, text="选择文件", command=self.select_file)
        select_btn.grid(row=0, column=2, padx=(0, 5), pady=(0, 5))
        
        # 清除按钮
        clear_btn = ttk.Button(parent, text="清除", command=self.clear_file)
        clear_btn.grid(row=0, column=3, pady=(0, 5))
        
        # 文件信息显示
        self.file_info_var = tk.StringVar()
        self.file_info_var.set("文件信息: 未选择文件")
        
        info_label = ttk.Label(parent, textvariable=self.file_info_var, foreground="gray")
        info_label.grid(row=1, column=0, columnspan=4, sticky=tk.W, pady=(0, 10))
        
    def create_search_config_area(self, parent):
        """创建搜索配置区域"""
        # 搜索配置框架
        search_frame = ttk.LabelFrame(parent, text="搜索配置", padding="5")
        search_frame.grid(row=2, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 10))
        search_frame.columnconfigure(1, weight=1)
        
        # 搜索内容输入
        ttk.Label(search_frame, text="搜索内容:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))

        # 绑定回车键开始搜索
        search_entry.bind('<Return>', lambda e: self.start_search())
        
        # 搜索按钮
        self.search_btn = ttk.Button(search_frame, text="开始搜索", command=self.start_search)
        self.search_btn.grid(row=0, column=2, padx=(0, 5))
        
        # 停止按钮
        self.stop_btn = ttk.Button(search_frame, text="停止", command=self.stop_search, state="disabled")
        self.stop_btn.grid(row=0, column=3)
        
        # 搜索选项
        options_frame = ttk.Frame(search_frame)
        options_frame.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 复选框选项
        self.regex_var = tk.BooleanVar()
        self.case_sensitive_var = tk.BooleanVar()
        self.whole_word_var = tk.BooleanVar()
        
        ttk.Checkbutton(options_frame, text="正则表达式", variable=self.regex_var).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Checkbutton(options_frame, text="大小写敏感", variable=self.case_sensitive_var).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Checkbutton(options_frame, text="整词匹配", variable=self.whole_word_var).pack(side=tk.LEFT, padx=(0, 10))
        
        # 上下文行数设置
        ttk.Label(options_frame, text="上下文行数:").pack(side=tk.LEFT, padx=(20, 5))
        self.context_var = tk.StringVar(value="2")
        context_spin = ttk.Spinbox(options_frame, from_=0, to=10, width=5, textvariable=self.context_var)
        context_spin.pack(side=tk.LEFT)

        # 高级设置框架
        advanced_frame = ttk.LabelFrame(search_frame, text="高级设置", padding="5")
        advanced_frame.grid(row=2, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(5, 0))

        # 并发线程数设置
        ttk.Label(advanced_frame, text="并发线程数:").pack(side=tk.LEFT, padx=(0, 5))
        self.thread_count_var = tk.StringVar(value="1")
        thread_spin = ttk.Spinbox(advanced_frame, from_=1, to=8, width=5, textvariable=self.thread_count_var)
        thread_spin.pack(side=tk.LEFT, padx=(0, 20))

        # 最大结果数设置
        ttk.Label(advanced_frame, text="最大结果数:").pack(side=tk.LEFT, padx=(0, 5))
        self.max_results_var = tk.StringVar(value="1000")
        results_spin = ttk.Spinbox(advanced_frame, from_=100, to=10000, increment=100, width=8, textvariable=self.max_results_var)
        results_spin.pack(side=tk.LEFT)
        
    def create_result_display_area(self, parent):
        """创建结果显示区域"""
        # 结果显示框架
        result_frame = ttk.LabelFrame(parent, text="搜索结果", padding="5")
        result_frame.grid(row=3, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview来显示结果
        columns = ("line_num", "content", "matches")
        self.result_tree = ttk.Treeview(result_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        self.result_tree.heading("line_num", text="行号")
        self.result_tree.heading("content", text="内容")
        self.result_tree.heading("matches", text="匹配数")

        # 设置列宽
        self.result_tree.column("line_num", width=80, minwidth=60)
        self.result_tree.column("content", width=700, minwidth=200)
        self.result_tree.column("matches", width=80, minwidth=60)

        # 设置行样式
        self.result_tree.tag_configure('match_line', background='#E8F4FD')
        self.result_tree.tag_configure('context_line', background='#F5F5F5', foreground='#666666')

        # 绑定双击事件
        self.result_tree.bind('<Double-1>', self.on_result_double_click)
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_tree.yview)
        h_scrollbar = ttk.Scrollbar(result_frame, orient=tk.HORIZONTAL, command=self.result_tree.xview)
        
        self.result_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.result_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=4, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(5, 0))
        status_frame.columnconfigure(0, weight=1)
        
        # 状态信息
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")

        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.grid(row=0, column=0, sticky=tk.W)

        # 性能信息（可选显示）
        self.performance_var = tk.StringVar()
        self.performance_var.set("")

        self.performance_label = ttk.Label(status_frame, textvariable=self.performance_var, foreground="blue")
        self.performance_label.grid(row=1, column=0, sticky=tk.W)
        
        # 保存结果按钮
        self.save_btn = ttk.Button(status_frame, text="保存结果", command=self.save_results, state="disabled")
        self.save_btn.grid(row=0, column=1, sticky=tk.E)
        
    def select_file(self):
        """选择文件"""
        file_path = filedialog.askopenfilename(
            title="选择要过滤的文件",
            filetypes=[
                ("文本文件", "*.txt"),
                ("日志文件", "*.log"),
                ("CSV文件", "*.csv"),
                ("JSON文件", "*.json"),
                ("XML文件", "*.xml"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self.load_file(file_path)

    def load_file(self, file_path):
        """加载文件并显示信息"""
        try:
            # 验证文件
            validation = validate_file_path(file_path)
            if not validation['is_valid']:
                error_msg = "文件验证失败:\n" + "\n".join(validation['errors'])
                messagebox.showerror("文件错误", error_msg)
                return

            # 获取文件详细信息
            self.current_file_info = get_file_info(file_path)

            # 打开文件
            processor_info = self.file_processor.open_file(file_path)

            # 更新界面
            self.selected_file = file_path
            self.file_path_var.set(file_path)

            # 显示详细文件信息
            file_name = self.current_file_info['name']
            file_size = self.current_file_info['size_formatted']
            encoding = processor_info['encoding']
            is_large = "是" if self.current_file_info['is_large_file'] else "否"

            info_text = f"文件: {file_name} | 大小: {file_size} | 编码: {encoding} | 大文件: {is_large}"
            self.file_info_var.set(info_text)

            # 显示状态
            self.status_var.set(f"文件已加载: {file_name}")

        except Exception as e:
            error_msg = f"加载文件失败: {str(e)}"
            self.file_info_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
                
    def clear_file(self):
        """清除选择的文件"""
        self.selected_file = None
        self.current_file_info = None
        self.file_path_var.set("请选择要过滤的文件...")
        self.file_info_var.set("文件信息: 未选择文件")

        # 重置文件处理器
        self.file_processor.reset_reading()

        # 清除搜索结果
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # 重置状态
        self.status_var.set("就绪")
        self.save_btn.config(state="disabled")
            
    def start_search(self):
        """开始搜索"""
        if not self.selected_file:
            messagebox.showwarning("警告", "请先选择要搜索的文件")
            return

        search_text = self.search_var.get().strip()
        if not search_text:
            messagebox.showwarning("警告", "请输入搜索内容")
            return

        if self.is_searching:
            messagebox.showwarning("警告", "已有搜索任务在进行中")
            return

        try:
            # 获取用户设置的参数
            try:
                max_results = int(self.max_results_var.get())
                thread_count = int(self.thread_count_var.get())
                context_lines = int(self.context_var.get())
            except ValueError:
                messagebox.showerror("参数错误", "请输入有效的数字参数")
                return

            # 验证参数范围
            # if max_results < 1 or max_results > 10000:
            #     messagebox.showerror("参数错误", "最大结果数必须在1-10000之间")
            #     return

            if thread_count < 1 or thread_count > 1000:
                messagebox.showerror("参数错误", "并发线程数必须在1-1000之间")
                return

            # 创建搜索配置
            config = SearchConfig(
                pattern=search_text,
                regex=self.regex_var.get(),
                case_sensitive=self.case_sensitive_var.get(),
                whole_word=self.whole_word_var.get(),
                context_lines=context_lines,
                max_results=max_results,
                thread_count=thread_count
            )

            # 验证搜索模式
            validation = self.filter_manager.search_engine.validate_pattern(
                config.pattern, config.regex
            )
            if not validation['is_valid']:
                messagebox.showerror("搜索错误", validation['error_message'])
                return

            # 更新界面状态
            self.is_searching = True
            self.search_btn.config(state="disabled")
            self.stop_btn.config(state="normal")
            self.save_btn.config(state="disabled")

            # 清除之前的结果
            self.current_search_results = []
            for item in self.result_tree.get_children():
                self.result_tree.delete(item)

            # 启动异步搜索
            self.filter_manager.filter_file_async(self.selected_file, config)

        except Exception as e:
            messagebox.showerror("搜索错误", f"启动搜索失败: {str(e)}")
            self.reset_search_ui()
        
    def stop_search(self):
        """停止搜索"""
        if self.is_searching:
            self.filter_manager.cancel_filter()
            self.status_var.set("正在停止搜索...")
        else:
            self.reset_search_ui()
        
    def save_results(self):
        """保存搜索结果"""
        if not self.current_search_results:
            messagebox.showinfo("提示", "没有搜索结果可以保存")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存搜索结果",
            defaultextension=".txt",
            filetypes=[
                ("文本文件", "*.txt"),
                ("CSV文件", "*.csv"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                # 询问保存选项
                include_context = messagebox.askyesno(
                    "保存选项",
                    "是否包含上下文行？\n\n"
                    "选择'是'将包含匹配行的前后文\n"
                    "选择'否'只保存匹配行本身"
                )

                # 使用过滤管理器保存结果
                success = self.filter_manager.save_results_to_file(
                    file_path,
                    include_context=include_context,
                    include_line_numbers=True
                )

                if success:
                    messagebox.showinfo("成功", f"搜索结果已保存到: {file_path}")
                else:
                    messagebox.showerror("错误", "保存文件失败")

            except Exception as e:
                messagebox.showerror("错误", f"保存文件失败: {str(e)}")
                
    def format_file_size(self, size_bytes):
        """格式化文件大小显示"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
            
        return f"{size_bytes:.1f} {size_names[i]}"

    def reset_search_ui(self):
        """重置搜索界面状态"""
        self.is_searching = False
        self.search_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        if self.current_search_results:
            self.save_btn.config(state="normal")
        else:
            self.save_btn.config(state="disabled")

    def on_search_progress(self, progress):
        """搜索进度回调"""
        # 更新状态栏
        status_text = f"{progress.status} - 进度: {progress.progress_percent:.1f}% - 找到 {progress.matches_found} 个匹配"
        if progress.elapsed_time > 0:
            status_text += f" - 耗时: {progress.elapsed_time:.1f}s"
        if progress.estimated_remaining > 0:
            status_text += f" - 剩余: {progress.estimated_remaining:.1f}s"

        self.status_var.set(status_text)

        # 强制更新界面
        self.root.update_idletasks()

    def on_search_result(self, result):
        """搜索结果回调"""
        # 添加到结果列表
        self.current_search_results.append(result)

        # 在界面中显示结果（限制显示数量以避免界面卡顿）
        if len(self.current_search_results) <= 100:
            self.add_result_to_tree(result)

        # 强制更新界面
        self.root.update_idletasks()

    def add_result_to_tree(self, result):
        """将搜索结果添加到结果树中"""
        # 格式化显示内容
        content = result.line_content.strip()
        if len(content) > 100:
            content = content[:97] + "..."

        # 计算匹配数量
        match_count = len(result.match_positions)

        # 插入匹配行
        item_id = self.result_tree.insert(
            "", "end",
            values=(result.line_number, content, match_count),
            tags=('match_line',)
        )

        # 如果有上下文行，也显示出来
        if hasattr(result, 'context_before') and result.context_before:
            for i, ctx_line in enumerate(result.context_before):
                ctx_content = ctx_line.strip()
                if len(ctx_content) > 100:
                    ctx_content = ctx_content[:97] + "..."

                ctx_line_num = result.line_number - len(result.context_before) + i
                self.result_tree.insert(
                    "", "end",
                    values=(f"{ctx_line_num} (前)", ctx_content, ""),
                    tags=('context_line',)
                )

        if hasattr(result, 'context_after') and result.context_after:
            for i, ctx_line in enumerate(result.context_after):
                ctx_content = ctx_line.strip()
                if len(ctx_content) > 100:
                    ctx_content = ctx_content[:97] + "..."

                ctx_line_num = result.line_number + i + 1
                self.result_tree.insert(
                    "", "end",
                    values=(f"{ctx_line_num} (后)", ctx_content, ""),
                    tags=('context_line',)
                )

    def on_search_complete(self, result):
        """搜索完成回调"""
        self.reset_search_ui()

        if result['success']:
            total_results = len(result['results'])
            elapsed_time = result.get('elapsed_time', 0)

            if result.get('cancelled', False):
                status_text = f"搜索已取消 - 找到 {total_results} 个匹配项"
            else:
                status_text = f"搜索完成 - 找到 {total_results} 个匹配项 - 耗时: {elapsed_time:.3f}s"

            self.status_var.set(status_text)

            # 如果结果太多，只显示前100个，并提示用户
            if total_results > 100:
                messagebox.showinfo(
                    "搜索完成",
                    f"搜索完成！\n"
                    f"总共找到 {total_results} 个匹配项\n"
                    f"界面只显示前100个结果\n"
                    f"可以保存完整结果到文件"
                )

                # 显示剩余结果
                for i, search_result in enumerate(result['results'][100:], 101):
                    if i > 200:  # 最多显示200个
                        break
                    content = search_result.line_content.strip()
                    if len(content) > 100:
                        content = content[:97] + "..."
                    self.result_tree.insert("", "end", values=(search_result.line_number, content))

            # 显示搜索摘要
            if 'summary' in result:
                summary = result['summary']
                print(f"搜索摘要: 总匹配数={summary['total_matches']}, 唯一行数={summary['unique_lines']}")

        else:
            error_msg = "搜索失败:\n" + "\n".join(result.get('errors', ['未知错误']))
            self.status_var.set("搜索失败")
            messagebox.showerror("搜索错误", error_msg)

    def highlight_search_results(self):
        """高亮显示搜索结果中的匹配文本"""
        # TODO: 实现结果高亮功能
        pass

    def setup_shortcuts(self):
        """设置快捷键"""
        # Ctrl+O: 打开文件
        self.root.bind('<Control-o>', lambda e: self.select_file())

        # Ctrl+F: 聚焦到搜索框
        self.root.bind('<Control-f>', lambda e: self.search_var.get() and None)

        # F3: 开始搜索
        self.root.bind('<F3>', lambda e: self.start_search())

        # Escape: 停止搜索
        self.root.bind('<Escape>', lambda e: self.stop_search())

        # Ctrl+S: 保存结果
        self.root.bind('<Control-s>', lambda e: self.save_results())

        # Enter: 在搜索框中按回车开始搜索
        # 这个会在创建搜索框时绑定

    def setup_drag_drop(self):
        """设置拖拽文件支持"""
        # 简单的拖拽支持（Windows）
        try:
            # 绑定拖拽事件
            self.root.drop_target_register('DND_Files')
            self.root.dnd_bind('<<Drop>>', self.on_file_drop)
        except:
            # 如果不支持拖拽，忽略错误
            pass

    def on_file_drop(self, event):
        """处理文件拖拽事件"""
        try:
            # 获取拖拽的文件路径
            files = event.data.split()
            if files:
                file_path = files[0].strip('{}')  # 移除可能的大括号
                self.load_file(file_path)
        except Exception as e:
            messagebox.showerror("错误", f"拖拽文件失败: {str(e)}")

    def show_file_info_dialog(self):
        """显示文件详细信息对话框"""
        if not self.current_file_info:
            messagebox.showinfo("提示", "请先选择文件")
            return

        info = self.current_file_info
        info_text = f"""文件详细信息:

文件名: {info['name']}
文件路径: {info['path']}
文件大小: {info['size_formatted']} ({info['size']} 字节)
创建时间: {info.get('created_time', '未知')}
修改时间: {info.get('modified_time', '未知')}
文件扩展名: {info.get('extension', '无')}
是否为文本文件: {'是' if info.get('is_text_file', False) else '否'}
是否为大文件: {'是' if info.get('is_large_file', False) else '否'}"""

        messagebox.showinfo("文件信息", info_text)

    def show_search_help(self):
        """显示搜索帮助对话框"""
        help_text = """搜索帮助:

基本搜索:
• 输入要查找的文本，程序会找到包含该文本的所有行

正则表达式搜索:
• 勾选"正则表达式"选项
• 支持标准正则表达式语法
• 例如: \\d+ (匹配数字), [a-zA-Z]+ (匹配字母)

搜索选项:
• 大小写敏感: 区分大小写字母
• 整词匹配: 只匹配完整的单词
• 上下文行数: 显示匹配行前后的行数

高级设置:
• 并发线程数: 设置搜索使用的线程数量 (1-8)
  - 单线程: 适合小文件，支持完整上下文
  - 多线程: 适合大文件，提高搜索速度
• 最大结果数: 限制搜索结果的最大数量 (100-10000)

快捷键:
• Ctrl+O: 打开文件
• Ctrl+F: 聚焦搜索框
• F3: 开始搜索
• Escape: 停止搜索
• Ctrl+S: 保存结果"""

        messagebox.showinfo("搜索帮助", help_text)

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开文件...", command=self.select_file, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="文件信息", command=self.show_file_info_dialog)
        file_menu.add_separator()
        file_menu.add_command(label="保存结果...", command=self.save_results, accelerator="Ctrl+S")
        file_menu.add_command(label="高级导出...", command=self.export_results_advanced)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)

        # 搜索菜单
        search_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="搜索", menu=search_menu)
        search_menu.add_command(label="开始搜索", command=self.start_search, accelerator="F3")
        search_menu.add_command(label="停止搜索", command=self.stop_search, accelerator="Escape")
        search_menu.add_separator()
        search_menu.add_command(label="清除结果", command=self.clear_results)
        search_menu.add_separator()
        search_menu.add_command(label="优化设置", command=self.optimize_search_settings)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="性能监控", command=self.show_performance_dialog)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="搜索帮助", command=self.show_search_help)
        help_menu.add_command(label="关于", command=self.show_about)

    def clear_results(self):
        """清除搜索结果"""
        self.current_search_results = []
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)
        self.status_var.set("结果已清除")
        self.save_btn.config(state="disabled")

    def show_about(self):
        """显示关于对话框"""
        about_text = """大文件过滤器 v1.0.0

一个高效的大文件内容搜索和过滤工具

特性:
• 支持GB级大文件处理
• 流式读取，内存占用低
• 支持正则表达式搜索
• 实时搜索进度显示
• 多种搜索选项
• 结果导出功能

开发: AI Assistant
技术: Python + tkinter"""

        messagebox.showinfo("关于", about_text)

    def on_result_double_click(self, event):
        """处理结果双击事件"""
        selection = self.result_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.result_tree.item(item, 'values')

        if len(values) >= 2:
            line_num_str = values[0]
            content = values[1]

            # 尝试解析行号
            try:
                if '(' in line_num_str:
                    line_num = int(line_num_str.split('(')[0].strip())
                else:
                    line_num = int(line_num_str)

                # 查找对应的搜索结果
                matching_result = None
                for result in self.current_search_results:
                    if result.line_number == line_num:
                        matching_result = result
                        break

                if matching_result:
                    self.show_result_detail(matching_result)
                else:
                    # 显示基本信息
                    detail_text = f"行号: {line_num}\n内容: {content}"
                    messagebox.showinfo("行详情", detail_text)

            except ValueError:
                messagebox.showinfo("行详情", f"内容: {content}")

    def show_result_detail(self, result):
        """显示搜索结果详情"""
        # 创建详情窗口
        detail_window = tk.Toplevel(self.root)
        detail_window.title(f"行 {result.line_number} 详情")
        detail_window.geometry("600x400")
        detail_window.transient(self.root)
        detail_window.grab_set()

        # 创建文本框显示详情
        text_frame = ttk.Frame(detail_window, padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True)

        # 文本显示区域
        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 构建详情内容
        detail_content = f"行号: {result.line_number}\n"
        detail_content += f"匹配数: {len(result.match_positions)}\n"
        detail_content += f"匹配位置: {result.match_positions}\n\n"

        # 显示上下文
        if hasattr(result, 'context_before') and result.context_before:
            detail_content += "=== 前文 ===\n"
            for i, line in enumerate(result.context_before):
                line_num = result.line_number - len(result.context_before) + i
                detail_content += f"{line_num:4d}: {line}\n"
            detail_content += "\n"

        # 显示匹配行（高亮）
        detail_content += "=== 匹配行 ===\n"
        detail_content += f"{result.line_number:4d}: {result.line_content}\n\n"

        # 显示后文
        if hasattr(result, 'context_after') and result.context_after:
            detail_content += "=== 后文 ===\n"
            for i, line in enumerate(result.context_after):
                line_num = result.line_number + i + 1
                detail_content += f"{line_num:4d}: {line}\n"

        # 插入内容
        text_widget.insert(tk.END, detail_content)
        text_widget.config(state=tk.DISABLED)

        # 高亮匹配行
        match_line_start = detail_content.find(f"{result.line_number:4d}: {result.line_content}")
        if match_line_start != -1:
            line_start = detail_content.count('\n', 0, match_line_start) + 1
            text_widget.tag_add("match_highlight", f"{line_start}.0", f"{line_start}.end")
            text_widget.tag_config("match_highlight", background="yellow")

        # 按钮框架
        button_frame = ttk.Frame(detail_window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        # 复制按钮
        copy_btn = ttk.Button(
            button_frame,
            text="复制内容",
            command=lambda: self.copy_to_clipboard(detail_content)
        )
        copy_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 关闭按钮
        close_btn = ttk.Button(button_frame, text="关闭", command=detail_window.destroy)
        close_btn.pack(side=tk.RIGHT)

    def copy_to_clipboard(self, text):
        """复制文本到剪贴板"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        messagebox.showinfo("提示", "内容已复制到剪贴板")

    def export_results_advanced(self):
        """高级结果导出"""
        if not self.current_search_results:
            messagebox.showinfo("提示", "没有搜索结果可以导出")
            return

        # 创建导出选项窗口
        export_window = tk.Toplevel(self.root)
        export_window.title("导出选项")
        export_window.geometry("400x300")
        export_window.transient(self.root)
        export_window.grab_set()

        # 导出选项
        options_frame = ttk.LabelFrame(export_window, text="导出选项", padding="10")
        options_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 格式选择
        format_var = tk.StringVar(value="txt")
        ttk.Label(options_frame, text="导出格式:").pack(anchor=tk.W)
        ttk.Radiobutton(options_frame, text="文本文件 (.txt)", variable=format_var, value="txt").pack(anchor=tk.W)
        ttk.Radiobutton(options_frame, text="CSV文件 (.csv)", variable=format_var, value="csv").pack(anchor=tk.W)
        ttk.Radiobutton(options_frame, text="HTML文件 (.html)", variable=format_var, value="html").pack(anchor=tk.W)

        # 内容选择
        ttk.Label(options_frame, text="\n包含内容:").pack(anchor=tk.W)
        include_context_var = tk.BooleanVar(value=True)
        include_line_numbers_var = tk.BooleanVar(value=True)
        include_match_positions_var = tk.BooleanVar(value=False)

        ttk.Checkbutton(options_frame, text="包含上下文行", variable=include_context_var).pack(anchor=tk.W)
        ttk.Checkbutton(options_frame, text="包含行号", variable=include_line_numbers_var).pack(anchor=tk.W)
        ttk.Checkbutton(options_frame, text="包含匹配位置", variable=include_match_positions_var).pack(anchor=tk.W)

        # 按钮
        button_frame = ttk.Frame(export_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        def do_export():
            file_format = format_var.get()
            extensions = {"txt": ".txt", "csv": ".csv", "html": ".html"}
            filetypes = {
                "txt": [("文本文件", "*.txt")],
                "csv": [("CSV文件", "*.csv")],
                "html": [("HTML文件", "*.html")]
            }

            file_path = filedialog.asksaveasfilename(
                title="导出搜索结果",
                defaultextension=extensions[file_format],
                filetypes=filetypes[file_format] + [("所有文件", "*.*")]
            )

            if file_path:
                success = self.export_results_to_format(
                    file_path, file_format,
                    include_context_var.get(),
                    include_line_numbers_var.get(),
                    include_match_positions_var.get()
                )

                if success:
                    messagebox.showinfo("成功", f"结果已导出到: {file_path}")
                    export_window.destroy()
                else:
                    messagebox.showerror("错误", "导出失败")

        ttk.Button(button_frame, text="导出", command=do_export).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="取消", command=export_window.destroy).pack(side=tk.RIGHT)

    def export_results_to_format(self, file_path, file_format, include_context,
                                include_line_numbers, include_match_positions):
        """导出结果到指定格式"""
        try:
            if file_format == "txt":
                return self.export_to_txt(file_path, include_context, include_line_numbers, include_match_positions)
            elif file_format == "csv":
                return self.export_to_csv(file_path, include_context, include_line_numbers, include_match_positions)
            elif file_format == "html":
                return self.export_to_html(file_path, include_context, include_line_numbers, include_match_positions)
            return False
        except Exception as e:
            print(f"导出失败: {e}")
            return False

    def export_to_txt(self, file_path, include_context, include_line_numbers, include_match_positions):
        """导出为文本格式"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"# 搜索结果导出\n")
            f.write(f"# 总共找到 {len(self.current_search_results)} 个匹配项\n")
            f.write(f"# 导出时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            for i, result in enumerate(self.current_search_results, 1):
                f.write(f"=== 结果 {i} ===\n")

                if include_line_numbers:
                    f.write(f"行号: {result.line_number}\n")

                if include_match_positions:
                    f.write(f"匹配位置: {result.match_positions}\n")

                # 上下文
                if include_context and hasattr(result, 'context_before') and result.context_before:
                    f.write("前文:\n")
                    for ctx_line in result.context_before:
                        f.write(f"  {ctx_line}\n")

                # 匹配行
                f.write(f"匹配: {result.line_content}\n")

                # 后文
                if include_context and hasattr(result, 'context_after') and result.context_after:
                    f.write("后文:\n")
                    for ctx_line in result.context_after:
                        f.write(f"  {ctx_line}\n")

                f.write("\n")

        return True

    def export_to_csv(self, file_path, include_context, include_line_numbers, include_match_positions):
        """导出为CSV格式"""
        import csv

        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # 写入标题行
            headers = []
            if include_line_numbers:
                headers.append('行号')
            headers.append('内容')
            if include_match_positions:
                headers.append('匹配位置')
            if include_context:
                headers.extend(['前文', '后文'])

            writer.writerow(headers)

            # 写入数据
            for result in self.current_search_results:
                row = []

                if include_line_numbers:
                    row.append(result.line_number)

                row.append(result.line_content)

                if include_match_positions:
                    row.append(str(result.match_positions))

                if include_context:
                    context_before = '; '.join(result.context_before) if hasattr(result, 'context_before') and result.context_before else ''
                    context_after = '; '.join(result.context_after) if hasattr(result, 'context_after') and result.context_after else ''
                    row.extend([context_before, context_after])

                writer.writerow(row)

        return True

    def export_to_html(self, file_path, include_context, include_line_numbers, include_match_positions):
        """导出为HTML格式"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>搜索结果</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
        .result { border: 1px solid #ddd; margin-bottom: 10px; border-radius: 5px; }
        .result-header { background-color: #e8f4fd; padding: 8px; font-weight: bold; }
        .result-content { padding: 10px; }
        .match-line { background-color: #fff3cd; padding: 5px; margin: 5px 0; border-left: 3px solid #ffc107; }
        .context-line { color: #666; font-style: italic; margin: 2px 0; }
        .line-number { color: #007bff; font-weight: bold; }
        .match-positions { color: #28a745; font-size: 0.9em; }
    </style>
</head>
<body>
""")

            f.write(f"""<div class="header">
    <h1>搜索结果</h1>
    <p>总共找到 {len(self.current_search_results)} 个匹配项</p>
    <p>导出时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
</div>
""")

            for i, result in enumerate(self.current_search_results, 1):
                f.write(f'<div class="result">\n')
                f.write(f'<div class="result-header">结果 {i}</div>\n')
                f.write(f'<div class="result-content">\n')

                if include_line_numbers:
                    f.write(f'<div class="line-number">行号: {result.line_number}</div>\n')

                if include_match_positions:
                    f.write(f'<div class="match-positions">匹配位置: {result.match_positions}</div>\n')

                # 上下文
                if include_context and hasattr(result, 'context_before') and result.context_before:
                    f.write('<div><strong>前文:</strong></div>\n')
                    for ctx_line in result.context_before:
                        f.write(f'<div class="context-line">{self.html_escape(ctx_line)}</div>\n')

                # 匹配行
                f.write(f'<div class="match-line">{self.html_escape(result.line_content)}</div>\n')

                # 后文
                if include_context and hasattr(result, 'context_after') and result.context_after:
                    f.write('<div><strong>后文:</strong></div>\n')
                    for ctx_line in result.context_after:
                        f.write(f'<div class="context-line">{self.html_escape(ctx_line)}</div>\n')

                f.write('</div>\n')
                f.write('</div>\n')

            f.write("""</body>
</html>""")

        return True

    def html_escape(self, text):
        """HTML转义"""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))

    def on_performance_update(self, metrics):
        """性能监控回调"""
        try:
            if hasattr(self.filter_manager, 'performance_monitor') and self.filter_manager.performance_monitor:
                performance_text = self.filter_manager.performance_monitor.format_metrics_text(metrics)
                self.performance_var.set(performance_text)
        except Exception as e:
            print(f"性能更新错误: {e}")

    def show_performance_dialog(self):
        """显示性能监控对话框"""
        if not hasattr(self.filter_manager, 'performance_monitor') or not self.filter_manager.performance_monitor:
            messagebox.showinfo("提示", "性能监控功能不可用")
            return

        summary = self.filter_manager.get_performance_summary()
        if not summary:
            messagebox.showinfo("提示", "暂无性能数据")
            return

        # 创建性能窗口
        perf_window = tk.Toplevel(self.root)
        perf_window.title("性能监控")
        perf_window.geometry("500x400")
        perf_window.transient(self.root)

        # 创建文本显示区域
        text_frame = ttk.Frame(perf_window, padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 构建性能报告
        report = "=== 性能监控报告 ===\n\n"

        if 'memory_usage' in summary:
            mem = summary['memory_usage']
            report += f"内存使用:\n"
            report += f"  当前: {mem['current']:.1f} MB\n"
            report += f"  最大: {mem['max']:.1f} MB\n"
            report += f"  平均: {mem['avg']:.1f} MB\n\n"

        if 'cpu_usage' in summary:
            cpu = summary['cpu_usage']
            report += f"CPU使用:\n"
            report += f"  当前: {cpu['current']:.1f}%\n"
            report += f"  最大: {cpu['max']:.1f}%\n"
            report += f"  平均: {cpu['avg']:.1f}%\n\n"

        if 'processing_speed' in summary:
            speed = summary['processing_speed']
            report += f"处理速度:\n"
            report += f"  当前: {speed['current']:.0f} 行/秒\n"
            report += f"  最大: {speed['max']:.0f} 行/秒\n"
            report += f"  平均: {speed['avg']:.0f} 行/秒\n\n"

        if 'total_lines' in summary:
            report += f"总处理行数: {summary['total_lines']}\n"

        if 'elapsed_time' in summary:
            report += f"总耗时: {summary['elapsed_time']:.2f} 秒\n"

        text_widget.insert(tk.END, report)
        text_widget.config(state=tk.DISABLED)

        # 关闭按钮
        close_btn = ttk.Button(perf_window, text="关闭", command=perf_window.destroy)
        close_btn.pack(pady=10)

    def optimize_search_settings(self):
        """优化搜索设置"""
        if not self.selected_file:
            messagebox.showwarning("警告", "请先选择文件")
            return

        try:
            # 创建当前搜索配置
            current_config = SearchConfig(
                pattern=self.search_var.get() or "test",
                regex=self.regex_var.get(),
                case_sensitive=self.case_sensitive_var.get(),
                whole_word=self.whole_word_var.get(),
                context_lines=int(self.context_var.get()),
                max_results=int(self.max_results_var.get()),
                thread_count=int(self.thread_count_var.get())
            )

            # 获取资源估算
            estimation = self.filter_manager.estimate_search_resources(self.selected_file, current_config)

            # 获取优化建议
            optimized_config = self.filter_manager.optimize_search_config(self.selected_file, current_config)

            # 显示优化建议对话框
            self.show_optimization_dialog(estimation, current_config, optimized_config)

        except Exception as e:
            messagebox.showerror("错误", f"优化分析失败: {str(e)}")

    def show_optimization_dialog(self, estimation, current_config, optimized_config):
        """显示优化建议对话框"""
        opt_window = tk.Toplevel(self.root)
        opt_window.title("搜索优化建议")
        opt_window.geometry("600x500")
        opt_window.transient(self.root)
        opt_window.grab_set()

        # 创建笔记本控件
        notebook = ttk.Notebook(opt_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 资源估算标签页
        estimation_frame = ttk.Frame(notebook)
        notebook.add(estimation_frame, text="资源估算")

        est_text = tk.Text(estimation_frame, wrap=tk.WORD, font=('Consolas', 10))
        est_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        est_content = "=== 资源估算 ===\n\n"
        if 'file_size_mb' in estimation:
            est_content += f"文件大小: {estimation['file_size_mb']:.1f} MB\n"
        if 'estimated_lines' in estimation:
            est_content += f"估算行数: {estimation['estimated_lines']:,}\n"
        if 'estimated_memory_mb' in estimation:
            est_content += f"预计内存使用: {estimation['estimated_memory_mb']:.1f} MB\n"
        if 'estimated_time_seconds' in estimation:
            est_content += f"预计搜索时间: {estimation['estimated_time_seconds']:.1f} 秒\n"

        if 'recommendations' in estimation and estimation['recommendations']:
            est_content += "\n=== 建议 ===\n"
            for rec in estimation['recommendations']:
                est_content += f"• {rec}\n"

        est_text.insert(tk.END, est_content)
        est_text.config(state=tk.DISABLED)

        # 优化设置标签页
        opt_frame = ttk.Frame(notebook)
        notebook.add(opt_frame, text="优化设置")

        opt_text = tk.Text(opt_frame, wrap=tk.WORD, font=('Consolas', 10))
        opt_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        opt_content = "=== 当前设置 vs 优化设置 ===\n\n"
        opt_content += f"最大结果数: {current_config.max_results} → {optimized_config.max_results}\n"
        opt_content += f"上下文行数: {current_config.context_lines} → {optimized_config.context_lines}\n"
        opt_content += f"并发线程数: {current_config.thread_count}\n"
        opt_content += f"正则表达式: {current_config.regex}\n"
        opt_content += f"大小写敏感: {current_config.case_sensitive}\n"
        opt_content += f"整词匹配: {current_config.whole_word}\n"

        opt_text.insert(tk.END, opt_content)
        opt_text.config(state=tk.DISABLED)

        # 按钮框架
        button_frame = ttk.Frame(opt_window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        def apply_optimization():
            # 应用优化设置
            self.context_var.set(str(optimized_config.context_lines))
            self.max_results_var.set(str(optimized_config.max_results))
            messagebox.showinfo("成功", "优化设置已应用")
            opt_window.destroy()

        ttk.Button(button_frame, text="应用优化", command=apply_optimization).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="关闭", command=opt_window.destroy).pack(side=tk.RIGHT)
