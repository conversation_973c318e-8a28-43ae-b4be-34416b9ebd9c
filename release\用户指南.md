# 大文件过滤器 - 用户指南

## 快速开始

1. **启动程序**
   - 双击 `大文件过滤器.exe` 启动程序

2. **选择文件**
   - 点击"选择文件"按钮，或直接拖拽文件到程序窗口
   - 支持的文件类型：.txt、.log、.csv、.json、.xml等文本文件

3. **设置搜索条件**
   - 在搜索框中输入要查找的内容
   - 可选择搜索选项：
     - ☑️ 正则表达式：使用正则表达式搜索
     - ☑️ 大小写敏感：区分大小写
     - ☑️ 整词匹配：只匹配完整单词
   - 设置上下文行数：显示匹配行前后的行数

4. **开始搜索**
   - 点击"开始搜索"按钮或按F3键
   - 程序会实时显示搜索进度和结果
   - 可以随时点击"停止"按钮中断搜索

5. **查看结果**
   - 搜索结果显示在下方表格中
   - 双击任意结果行可查看详细信息
   - 状态栏显示匹配数量和搜索统计

6. **保存结果**
   - 点击"保存结果"按钮
   - 选择保存格式：文本文件、CSV文件、HTML文件
   - 可选择是否包含上下文行和行号

## 快捷键

- **Ctrl+O**: 打开文件
- **Ctrl+F**: 聚焦到搜索框
- **F3**: 开始搜索
- **Escape**: 停止搜索
- **Ctrl+S**: 保存结果

## 高级功能

### 正则表达式搜索
勾选"正则表达式"选项后，可以使用正则表达式进行复杂搜索：
- `\d+`: 匹配数字
- `[a-zA-Z]+`: 匹配字母
- `\w+@\w+\.\w+`: 匹配邮箱地址
- `\b\w{4}\b`: 匹配4个字母的单词

### 性能优化
- 程序会根据文件大小自动优化搜索参数
- 可通过"工具"菜单查看性能监控信息
- 建议大文件搜索时适当限制结果数量

### 高级导出
- 通过"文件"菜单的"高级导出"功能
- 支持多种格式和自定义选项
- HTML格式支持高亮显示和样式

## 故障排除

### 程序无法启动
- 确保系统支持Windows 7及以上版本
- 检查是否有杀毒软件误报

### 搜索速度慢
- 尝试减少上下文行数
- 限制最大结果数量
- 避免过于复杂的正则表达式

### 内存使用过高
- 程序会自动监控内存使用
- 大文件搜索时会自动优化参数
- 可通过性能监控查看详细信息

## 技术支持

如有问题或建议，请查看项目文档或联系开发者。

---
大文件过滤器 v1.0.0
