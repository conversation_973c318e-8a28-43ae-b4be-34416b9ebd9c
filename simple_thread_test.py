#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的多线程功能测试
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.search_engine import SearchConfig

def test_search_config():
    """测试搜索配置"""
    print("=== 测试搜索配置 ===")
    
    # 测试新的配置选项
    config = SearchConfig(
        pattern="测试",
        regex=False,
        case_sensitive=False,
        whole_word=False,
        context_lines=2,
        max_results=500,
        thread_count=4
    )
    
    print(f"✓ 搜索模式: {config.pattern}")
    print(f"✓ 正则表达式: {config.regex}")
    print(f"✓ 最大结果数: {config.max_results}")
    print(f"✓ 线程数: {config.thread_count}")
    print(f"✓ 上下文行数: {config.context_lines}")
    
    return True

def test_gui_integration():
    """测试GUI集成"""
    print("\n=== 测试GUI集成 ===")
    
    try:
        import tkinter as tk
        from gui.main_window import MainWindow
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        app = MainWindow(root)
        
        # 检查新的变量是否存在
        if hasattr(app, 'thread_count_var'):
            print("✓ 线程数设置变量存在")
            print(f"  默认值: {app.thread_count_var.get()}")
        else:
            print("❌ 线程数设置变量不存在")
            return False
        
        if hasattr(app, 'max_results_var'):
            print("✓ 最大结果数设置变量存在")
            print(f"  默认值: {app.max_results_var.get()}")
        else:
            print("❌ 最大结果数设置变量不存在")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return False

if __name__ == "__main__":
    print("简单多线程功能测试")
    print("=" * 40)
    
    tests = [
        test_search_config,
        test_gui_integration
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("测试失败")
        except Exception as e:
            print(f"测试异常: {e}")
    
    print(f"\n测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 基本功能测试通过！")
    else:
        print("❌ 部分测试失败")
