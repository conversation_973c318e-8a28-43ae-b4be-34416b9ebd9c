#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件处理器模块

负责大文件的读取、编码检测和流式处理等核心功能。
"""

import os
import mmap
import threading
from typing import Generator, Optional, Dict, Any

# 尝试相对导入，如果失败则使用绝对导入
try:
    from ..utils.encoding_utils import detect_encoding, safe_decode, get_file_encoding_info
except ImportError:
    import sys
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from utils.encoding_utils import detect_encoding, safe_decode, get_file_encoding_info


class FileProcessor:
    """文件处理器类 - 负责文件的读取和处理"""

    def __init__(self, buffer_size: int = 8192):
        """
        初始化文件处理器

        参数:
            buffer_size: 缓冲区大小（字节），默认8KB
        """
        self.buffer_size = buffer_size
        self.current_file = None
        self.current_encoding = None
        self.file_size = 0
        self.bytes_read = 0
        self._stop_reading = False

    def open_file(self, file_path: str) -> Dict[str, Any]:
        """
        打开文件并获取基本信息

        参数:
            file_path: 文件路径

        返回:
            文件信息字典
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            if not os.path.isfile(file_path):
                raise ValueError(f"不是有效的文件: {file_path}")

            # 获取文件信息
            self.file_size = os.path.getsize(file_path)

            # 检测编码
            encoding_info = get_file_encoding_info(file_path)
            self.current_encoding = encoding_info['encoding']

            # 保存文件路径
            self.current_file = file_path
            self.bytes_read = 0
            self._stop_reading = False

            return {
                'file_path': file_path,
                'file_size': self.file_size,
                'encoding': self.current_encoding,
                'encoding_valid': encoding_info['is_valid'],
                'confidence': encoding_info['confidence']
            }

        except Exception as e:
            raise Exception(f"打开文件失败: {str(e)}")

    def read_file_stream(self, file_path: str, encoding: Optional[str] = None) -> Generator[tuple, None, None]:
        """
        流式读取文件内容

        参数:
            file_path: 文件路径
            encoding: 指定编码格式，如果为None则自动检测

        返回:
            生成器，返回 (行号, 行内容) 元组
        """
        if encoding is None:
            encoding = self.current_encoding or detect_encoding(file_path)

        try:
            line_number = 0
            self.bytes_read = 0

            with open(file_path, 'rb') as file:
                buffer = b''

                while not self._stop_reading:
                    # 读取数据块
                    chunk = file.read(self.buffer_size)
                    if not chunk:
                        break

                    self.bytes_read += len(chunk)
                    buffer += chunk

                    # 处理缓冲区中的完整行
                    while b'\n' in buffer:
                        line_bytes, buffer = buffer.split(b'\n', 1)

                        # 处理Windows风格的换行符
                        if line_bytes.endswith(b'\r'):
                            line_bytes = line_bytes[:-1]

                        # 解码行内容
                        try:
                            line_content = safe_decode(line_bytes, encoding)
                            line_number += 1
                            yield (line_number, line_content)
                        except Exception as e:
                            # 如果解码失败，跳过这一行并记录错误
                            line_number += 1
                            yield (line_number, f"[解码错误: {str(e)}]")

                # 处理最后一行（如果没有换行符结尾）
                if buffer and not self._stop_reading:
                    try:
                        line_content = safe_decode(buffer, encoding)
                        line_number += 1
                        yield (line_number, line_content)
                    except Exception as e:
                        line_number += 1
                        yield (line_number, f"[解码错误: {str(e)}]")

        except Exception as e:
            raise Exception(f"读取文件失败: {str(e)}")

    def read_file_lines_range(self, file_path: str, start_line: int, end_line: int,
                             encoding: Optional[str] = None) -> Generator[tuple, None, None]:
        """
        读取文件指定行范围的内容

        参数:
            file_path: 文件路径
            start_line: 起始行号（从1开始）
            end_line: 结束行号（包含）
            encoding: 编码格式

        返回:
            生成器，返回 (行号, 行内容) 元组
        """
        current_line = 0

        for line_num, line_content in self.read_file_stream(file_path, encoding):
            current_line = line_num

            if current_line < start_line:
                continue
            elif current_line > end_line:
                break
            else:
                yield (line_num, line_content)

    def get_file_preview(self, file_path: str, max_lines: int = 100,
                        encoding: Optional[str] = None) -> list:
        """
        获取文件预览（前N行）

        参数:
            file_path: 文件路径
            max_lines: 最大行数
            encoding: 编码格式

        返回:
            行内容列表
        """
        preview_lines = []

        try:
            for line_num, line_content in self.read_file_stream(file_path, encoding):
                preview_lines.append((line_num, line_content))

                if len(preview_lines) >= max_lines:
                    break

        except Exception as e:
            preview_lines.append((0, f"预览失败: {str(e)}"))

        return preview_lines

    def get_reading_progress(self) -> float:
        """
        获取读取进度

        返回:
            进度百分比 (0.0 - 1.0)
        """
        if self.file_size == 0:
            return 0.0
        return min(self.bytes_read / self.file_size, 1.0)

    def stop_reading(self):
        """停止文件读取"""
        self._stop_reading = True

    def reset_reading(self):
        """重置读取状态"""
        self._stop_reading = False
        self.bytes_read = 0

    def get_file_stats(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件统计信息

        参数:
            file_path: 文件路径

        返回:
            文件统计信息字典
        """
        try:
            stats = {
                'file_path': file_path,
                'file_size': 0,
                'line_count': 0,
                'encoding': 'unknown',
                'sample_content': []
            }

            # 获取文件大小
            stats['file_size'] = os.path.getsize(file_path)

            # 检测编码
            encoding_info = get_file_encoding_info(file_path)
            stats['encoding'] = encoding_info['encoding']

            # 统计行数和获取样本内容
            line_count = 0
            sample_lines = []

            for line_num, line_content in self.read_file_stream(file_path):
                line_count = line_num

                # 收集前10行作为样本
                if len(sample_lines) < 10:
                    sample_lines.append(line_content[:100])  # 限制每行长度

                # 如果文件很大，每1000行检查一次是否需要停止
                if line_count % 1000 == 0 and self._stop_reading:
                    break

            stats['line_count'] = line_count
            stats['sample_content'] = sample_lines

            return stats

        except Exception as e:
            return {
                'file_path': file_path,
                'file_size': 0,
                'line_count': 0,
                'encoding': 'unknown',
                'sample_content': [],
                'error': str(e)
            }

    def read_file_with_mmap(self, file_path: str, encoding: Optional[str] = None) -> Generator[tuple, None, None]:
        """
        使用内存映射读取大文件（适用于超大文件）

        参数:
            file_path: 文件路径
            encoding: 编码格式

        返回:
            生成器，返回 (行号, 行内容) 元组
        """
        if encoding is None:
            encoding = self.current_encoding or detect_encoding(file_path)

        try:
            with open(file_path, 'rb') as file:
                # 使用内存映射
                with mmap.mmap(file.fileno(), 0, access=mmap.ACCESS_READ) as mmapped_file:
                    line_number = 0
                    start_pos = 0

                    while start_pos < len(mmapped_file) and not self._stop_reading:
                        # 查找下一个换行符
                        end_pos = mmapped_file.find(b'\n', start_pos)

                        if end_pos == -1:
                            # 最后一行
                            line_bytes = mmapped_file[start_pos:]
                            start_pos = len(mmapped_file)
                        else:
                            line_bytes = mmapped_file[start_pos:end_pos]
                            start_pos = end_pos + 1

                        # 处理Windows风格的换行符
                        if line_bytes.endswith(b'\r'):
                            line_bytes = line_bytes[:-1]

                        # 解码行内容
                        try:
                            line_content = safe_decode(line_bytes, encoding)
                            line_number += 1
                            self.bytes_read = start_pos
                            yield (line_number, line_content)
                        except Exception as e:
                            line_number += 1
                            yield (line_number, f"[解码错误: {str(e)}]")

        except Exception as e:
            raise Exception(f"内存映射读取失败: {str(e)}")

    def choose_reading_method(self, file_path: str) -> str:
        """
        根据文件大小选择最适合的读取方法

        参数:
            file_path: 文件路径

        返回:
            推荐的读取方法 ('stream' 或 'mmap')
        """
        try:
            file_size = os.path.getsize(file_path)

            # 超过100MB的文件使用内存映射
            if file_size > 100 * 1024 * 1024:
                return 'mmap'
            else:
                return 'stream'

        except Exception:
            return 'stream'  # 默认使用流式读取

    def read_file_optimized(self, file_path: str, encoding: Optional[str] = None) -> Generator[tuple, None, None]:
        """
        优化的文件读取方法，自动选择最佳读取策略

        参数:
            file_path: 文件路径
            encoding: 编码格式

        返回:
            生成器，返回 (行号, 行内容) 元组
        """
        method = self.choose_reading_method(file_path)

        if method == 'mmap':
            return self.read_file_with_mmap(file_path, encoding)
        else:
            return self.read_file_stream(file_path, encoding)
