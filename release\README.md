# 大文件过滤器 (Large File Filter)

一个高效的大文件内容过滤和搜索工具，专门用于处理GB级别的文本文件，支持实时过滤显示和结果保存。

## 项目概述

### 核心问题
- 传统文本编辑器（如Notepad）打开大文件（2GB+）速度慢、占用内存大
- 用户只需要查找文件中的特定信息，不需要完整加载文件
- 需要高效的搜索和过滤功能

### 解决方案
开发一个轻量级的文件过滤器，采用流式读取技术，实现：
- 无需完整加载文件即可搜索
- 实时过滤显示结果
- 支持多种搜索模式（关键词、正则表达式等）
- 将过滤结果保存为新文件

## 功能特性

### 核心功能
1. **文件选择**：支持拖拽或浏览选择大文件
2. **实时过滤**：输入搜索条件后实时显示匹配结果
3. **多种搜索模式**：
   - 普通文本搜索
   - 正则表达式搜索
   - 大小写敏感/不敏感
   - 整词匹配
4. **结果预览**：在界面中显示匹配的行和上下文
5. **结果保存**：将过滤结果保存到新文件
6. **进度显示**：显示搜索进度和文件处理状态

### 高级功能
1. **行号显示**：显示匹配行在原文件中的行号
2. **上下文行**：可配置显示匹配行的前后几行
3. **统计信息**：显示匹配行数、文件大小等信息
4. **搜索历史**：保存最近的搜索条件
5. **编码检测**：自动检测文件编码格式

## 技术架构

### 开发语言
- **Python 3.8+**

### 核心技术栈
- **GUI框架**：tkinter（Python内置，无需额外依赖）
- **文件处理**：流式读取，逐行处理
- **正则表达式**：re模块
- **异步处理**：threading模块
- **编码检测**：chardet库

### 性能优化策略
1. **流式读取**：使用生成器逐行读取，避免内存溢出
2. **异步搜索**：搜索在后台线程进行，保持界面响应
3. **结果缓存**：缓存搜索结果，避免重复计算
4. **分块处理**：大文件分块读取和处理
5. **内存管理**：及时释放不需要的数据

## 项目结构

```
过滤文件器/
├── README.md                 # 项目说明文档
├── PRODUCT_DESIGN.md         # 产品设计文档
├── requirements.txt          # Python依赖包
├── main.py                   # 主程序入口
├── src/                      # 源代码目录
│   ├── __init__.py
│   ├── gui/                  # 界面相关
│   │   ├── __init__.py
│   │   ├── main_window.py    # 主窗口
│   │   └── components.py     # UI组件
│   ├── core/                 # 核心功能
│   │   ├── __init__.py
│   │   ├── file_processor.py # 文件处理器
│   │   ├── search_engine.py  # 搜索引擎
│   │   └── filter_manager.py # 过滤管理器
│   └── utils/                # 工具函数
│       ├── __init__.py
│       ├── file_utils.py     # 文件工具
│       └── encoding_utils.py # 编码工具
├── tests/                    # 测试文件
│   ├── __init__.py
│   ├── test_file_processor.py
│   └── test_search_engine.py
└── docs/                     # 文档目录
    ├── user_guide.md         # 用户指南
    └── api_reference.md      # API参考
```

## 快速开始

### 环境要求
- Python 3.8 或更高版本
- Windows/Linux/macOS

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行程序
```bash
python main.py
```

## 使用说明

1. **选择文件**：点击"选择文件"按钮或直接拖拽文件到程序窗口
2. **设置过滤条件**：输入搜索关键词或正则表达式
3. **选择搜索模式**：普通搜索或正则表达式搜索
4. **开始搜索**：点击"开始搜索"按钮
5. **查看结果**：在结果区域查看匹配的行
6. **保存结果**：点击"保存结果"将过滤后的内容保存到新文件

## 开发计划

详细的开发任务和进度请参考项目任务管理系统。

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
