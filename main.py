#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大文件过滤器 - 主程序入口

这是一个用于处理大型文本文件的过滤和搜索工具。
支持流式读取、实时搜索、正则表达式匹配等功能。

作者：AI Assistant
版本：1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from gui.main_window import MainWindow
except ImportError as e:
    print(f"导入错误：{e}")
    print("请确保所有依赖包已正确安装")
    sys.exit(1)


def main():
    """主函数 - 程序入口点"""
    try:
        # 创建主窗口
        root = tk.Tk()
        
        # 设置窗口基本属性
        root.title("大文件过滤器 v1.0.0")
        root.geometry("1000x700")
        root.minsize(800, 600)
        
        # 设置窗口图标（如果有的话）
        try:
            # root.iconbitmap("assets/icon.ico")  # 可以后续添加图标
            pass
        except:
            pass
        
        # 创建主应用程序
        app = MainWindow(root)
        
        # 启动GUI事件循环
        root.mainloop()
        
    except Exception as e:
        # 处理启动错误
        error_msg = f"程序启动失败：{str(e)}"
        print(error_msg)
        
        # 如果tkinter可用，显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror("启动错误", error_msg)
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
